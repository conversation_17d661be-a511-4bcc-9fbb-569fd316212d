"""
API routes for dataset management.
"""

from typing import List

from fastapi import APIRouter, HTTPException, Request, status
from pydantic import BaseModel, ConfigDict

from api.config import settings
from api.utils import get_supabase_profile
from api.utils.error_handlers import INTERNAL_SERVER_ERROR_RESPONSE, api_route_handler

# Import supabase client functions at runtime to avoid circular imports
from database import supabase_client
from database.models import Dataset

router = APIRouter(
    prefix="/datasets",
    tags=["datasets"],  # Groups endpoints under "datasets" in the docs
    responses=INTERNAL_SERVER_ERROR_RESPONSE,
)


class DebugSettings(BaseModel):
    """Response model for debug settings."""

    profile: str
    dev_url: str
    staging_url: str
    prod_url: str
    active_url: str
    active_key: str
    env_file: str

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "profile": "development",
                "dev_url": "https://dev-project.supabase.co",
                "staging_url": "https://staging-project.supabase.co",
                "prod_url": "https://prod-project.supabase.co",
                "active_url": "https://dev-project.supabase.co",
                "active_key": "abc123...",
                "env_file": ".env.local",
            }
        }
    )


@router.get(
    "/debug",
    response_model=DebugSettings,
    summary="Debug Supabase Settings",
    description=(
        "Returns the current Supabase configuration settings for debugging purposes."
    ),
)
@api_route_handler("accessing Supabase settings")
async def debug_settings(request: Request) -> DebugSettings:
    """Debug endpoint to check Supabase settings."""
    # Get the profile from the request header
    profile = get_supabase_profile(request)

    # Force a refresh of the client with the specified profile
    supabase_client.get_supabase_client.cache_clear()

    # Get credentials from settings
    credentials = settings.active_supabase_credentials

    return DebugSettings(
        profile=profile,
        dev_url=settings.SUPABASE_DEV_URL,
        staging_url=settings.SUPABASE_STAGING_URL,
        prod_url=settings.SUPABASE_URL,
        active_url=credentials.url,
        active_key=credentials.anon_key[:6] + "..." if credentials.anon_key else "",
        env_file=settings.model_config["env_file"],
    )


@router.get(
    "",
    response_model=List[Dataset],
    summary="Get All Datasets",
    description="Retrieves a list of all available datasets from the database.",
)
@api_route_handler("fetching datasets")
async def get_datasets(request: Request) -> List[Dataset]:
    """
    Get all datasets.

    Returns:
        List[Dataset]: A list of all datasets.

    Raises:
        HTTPException: If there's an error fetching the datasets.
    """
    # Get the profile from the request header
    profile = get_supabase_profile(request)

    datasets = supabase_client.fetch_data("datasets", profile=profile)
    return [Dataset(**dataset) for dataset in datasets]


@router.get(
    "/{dataset_id}",
    response_model=Dataset,
    summary="Get Dataset by ID",
    description="Retrieves a specific dataset by its UUID.",
    responses={
        status.HTTP_404_NOT_FOUND: {
            "description": "Dataset not found",
            "content": {
                "application/json": {
                    "example": {
                        "detail": (
                            "Dataset 123e4567-e89b-12d3-a456-************ not found"
                        )
                    }
                }
            },
        }
    },
)
@api_route_handler("fetching dataset")
async def get_dataset(request: Request, dataset_id: str) -> Dataset:
    """
    Get a dataset by ID.

    Args:
        request: The HTTP request object
        dataset_id (str): The UUID of the dataset to retrieve.

    Returns:
        Dataset: The requested dataset.

    Raises:
        HTTPException: If the dataset is not found or there's an error fetching it.
    """
    # Get the profile from the request header
    profile = get_supabase_profile(request)

    datasets = supabase_client.fetch_data(
        "datasets",
        {"filters": [{"column": "uuid", "value": dataset_id}]},
        profile=profile,
    )
    if not datasets:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Dataset {dataset_id} not found",
        )
    return Dataset(**datasets[0])
