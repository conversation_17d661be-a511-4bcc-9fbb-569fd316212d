"""
Training Data API routes.

This module provides endpoints for retrieving training data for model runs.
"""

from fastapi import APIRouter, Request

from api.utils import get_supabase_profile
from api.utils.error_handlers import INTERNAL_SERVER_ERROR_RESPONSE, api_route_handler
from database.services import TrainingDataService

router = APIRouter(
    prefix="/training_data",
    tags=["training_data", "internal"],
    responses=INTERNAL_SERVER_ERROR_RESPONSE,
)


@router.get(
    "/{model_run_uuid}",
    summary="Get Training Data",
    description="Retrieves all training data for a specific model run.",
)
@api_route_handler("retrieving training data")
async def get_training_data(request: Request, model_run_uuid: str):
    """Get training data for a specific model run."""
    # Get the profile from the request header
    profile = get_supabase_profile(request)

    # Use TrainingDataService to fetch all required data
    training_data = await TrainingDataService.get_training_data(
        model_run_uuid=model_run_uuid,
        profile=profile,
    )

    return training_data
