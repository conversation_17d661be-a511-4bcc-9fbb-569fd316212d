"""Utility functions for handling common API errors."""

import functools
from typing import Any, Callable, NoReturn, TypeVar

from fastapi import HTTPException, status
from pydantic import ValidationError
from supabase import PostgrestAPIError

from database.services.model_metadata_service import ModelMetadataError
from database.services.training_data_service import TrainingDataError

# Type variable for the return type of the decorated function
T = TypeVar("T")

# Common error response schema
INTERNAL_SERVER_ERROR_RESPONSE = {
    status.HTTP_500_INTERNAL_SERVER_ERROR: {
        "description": "Internal Server Error",
        "content": {
            "application/json": {
                "example": {"detail": "Internal server error occurred"}
            }
        },
    }
}


def handle_service_exceptions(e: Exception) -> NoReturn:
    """
    Handle service-specific exceptions and convert them to appropriate HTTP exceptions.

    Args:
        e: The exception to handle (TrainingDataError or ModelMetadataError)

    Raises:
        HTTPException: With appropriate status code and detail message
    """
    # Use 404 for not found errors, 400 for other client errors
    if "not found" in str(e).lower():
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e),
        ) from e
    # For other client errors
    raise HTTPException(
        status_code=status.HTTP_400_BAD_REQUEST,
        detail=str(e),
    ) from e


def api_route_handler(operation_description: str):
    """
    Decorator for API route functions to handle exceptions consistently.

    This decorator wraps API route functions with a consistent exception handling pattern.
    It handles service-specific exceptions (TrainingDataError, ModelMetadataError),
    HTTPExceptions, and general exceptions using the existing handle_api_error function.

    Args:
        operation_description: Description of the operation being performed
                              (used in error messages)

    Returns:
        Decorated function with consistent exception handling
    """

    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @functools.wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> T:
            try:
                return await func(*args, **kwargs)
            except (TrainingDataError, ModelMetadataError) as e:
                handle_service_exceptions(e)
            except HTTPException:
                raise
            except Exception as e:  # pylint: disable=broad-except
                handle_api_error(operation_description, e)

        return wrapper

    return decorator


def handle_api_error(operation: str, error: Exception) -> NoReturn:
    """
    Handle common API errors and raise appropriate HTTP exceptions.
    This function centralizes error handling logic for API endpoints.

    Args:
        operation: Description of the operation being performed (for error messages)
        error: The exception that was caught

    Raises:
        HTTPException: An appropriate HTTP exception based on the error type
    """
    # Handle validation errors (e.g., from Pydantic models)
    if isinstance(error, ValidationError):
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=f"Validation error while {operation}: {str(error)}",
        ) from error

    # Handle Supabase database errors
    if isinstance(error, PostgrestAPIError):
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Database error while {operation}: {str(error)}",
        ) from error

    # Handle connection errors
    if isinstance(error, ConnectionError):
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Connection error while {operation}: {str(error)}",
        ) from error

    # Handle key errors (missing data)
    if isinstance(error, KeyError):
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Missing data while {operation}: {str(error)}",
        ) from error

    # Handle type errors
    if isinstance(error, TypeError):
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Type error while {operation}: {str(error)}",
        ) from error

    # Handle value errors
    if isinstance(error, ValueError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid value while {operation}: {str(error)}",
        ) from error

    # Handle attribute errors
    if isinstance(error, AttributeError):
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Attribute error while {operation}: {str(error)}",
        ) from error

    # Handle index errors
    if isinstance(error, IndexError):
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Index error while {operation}: {str(error)}",
        ) from error

    # Log unexpected errors but don't expose details to the client
    print(f"Unexpected error while {operation}: {type(error).__name__}: {str(error)}")
    raise HTTPException(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        detail=f"An unexpected error occurred while {operation}",
    ) from error
