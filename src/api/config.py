"""
Configuration settings for the API using pydantic-settings.
Handles environment variables and default configurations for the API server.
"""

from pathlib import Path
from typing import Literal

from pydantic import Field
from pydantic_settings import BaseSettings


class SupabaseCredentials(BaseSettings):
    """Supabase credentials configuration per profile"""

    url: str
    anon_key: str

    model_config = {"extra": "allow"}


class Settings(BaseSettings):
    """API configuration settings class."""

    API_HOST: str = "0.0.0.0"
    API_PORT: int = 8000
    API_VERSION: str = "v1"
    API_PREFIX: str = f"/api/{API_VERSION}"

    # Supabase settings - default to development
    SUPABASE_PROFILE: Literal["development", "staging", "production", ""] = Field(
        default="development", description="Current Supabase profile"
    )

    # Profile-specific credentials
    SUPABASE_DEV_URL: str = ""
    SUPABASE_DEV_ANON_KEY: str = ""
    SUPABASE_STAGING_URL: str = ""
    SUPABASE_STAGING_ANON_KEY: str = ""
    SUPABASE_URL: str = ""  # Production URL
    SUPABASE_ANON_KEY: str = ""  # Production key

    model_config = {
        "env_prefix": "COINY_CLASSIFIER_",
        "env_file": str(Path(__file__).parent.parent.parent / ".env.local"),
        "env_file_encoding": "utf-8",
        "validate_assignment": True,
        "extra": "allow",  # Allow extra fields
    }

    @property
    def active_supabase_credentials(self) -> SupabaseCredentials:
        """Get the active Supabase credentials based on profile"""
        credentials_map = {
            "development": SupabaseCredentials(
                url=self.SUPABASE_DEV_URL, anon_key=self.SUPABASE_DEV_ANON_KEY
            ),
            "staging": SupabaseCredentials(
                url=self.SUPABASE_STAGING_URL, anon_key=self.SUPABASE_STAGING_ANON_KEY
            ),
            "production": SupabaseCredentials(
                url=self.SUPABASE_URL, anon_key=self.SUPABASE_ANON_KEY
            ),
            "": SupabaseCredentials(
                url=self.SUPABASE_DEV_URL, anon_key=self.SUPABASE_DEV_ANON_KEY
            ),
        }
        return credentials_map[self.SUPABASE_PROFILE]


settings = Settings()
