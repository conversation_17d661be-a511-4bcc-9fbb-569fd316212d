"""
Utility functions for visualizing data using Matplotlib.

This module provides functions to plot training/testing losses and accuracies,
Gaussian blurs with model predictions, feature map correlations, and feature map activations.
"""

# Collections of utils to visualize data

import matplotlib.pyplot as plt
import matplotlib_inline.backend_inline
import numpy as np
import torch

matplotlib_inline.backend_inline.set_matplotlib_formats("svg")


# Loss
def plot_losses(train_loss, test_loss, output_path=None):
    """
    Plots training and testing losses over epochs.

    Args:
        train_loss (list or np.ndarray): List of training losses per epoch.
        test_loss (list or np.ndarray): List of testing losses per epoch.
        output_path (str, optional): Path to save the plot. If None, shows the plot.
                                     Defaults to None.
    """
    fig, a_x = plt.subplots(1, 1, figsize=(16, 5))

    a_x.plot(train_loss, "s-", label="Train")
    a_x.plot(test_loss, "o-", label="Test")
    a_x.set_xlabel("Epochs")
    a_x.set_ylabel("Loss (MSE)")
    a_x.set_title("Model loss")
    a_x.legend()

    if output_path:
        plt.savefig(output_path)
        plt.close(fig)
    else:
        plt.show()


# Accuracy
def plot_accuracy(train_accuracy, test_accuracy, output_path=None):
    """
    Plots training and testing accuracies over epochs.

    Args:
        train_accuracy (list or np.ndarray): List of training accuracies per epoch.
        test_accuracy (list or np.ndarray): List of testing accuracies per epoch.
        output_path (str, optional): Path to save the plot. If None, shows the plot.
                                     Defaults to None.
    """
    fig, a_x = plt.subplots(1, 1, figsize=(16, 5))
    a_x.plot(train_accuracy, "s-", label="Train")
    a_x.plot(test_accuracy, "o-", label="Test")
    a_x.set_xlabel("Epochs")
    a_x.set_ylabel("Accuracy (%)")
    a_x.set_title(f"Final model test accuracy: {test_accuracy[-1]:.2f}%")
    a_x.legend()

    if output_path:
        plt.savefig(output_path)
        plt.close(fig)
    else:
        plt.show()


def plot_gaussian_blurs(y_true, y_pred, images, output_path=None):
    """
    Displays a grid of images with their true and predicted labels.

    Typically used to show original images and their model predictions, for example,
    in a binary classification task (e.g., original vs. blurred).

    Args:
        y_true (torch.Tensor): True labels for the images.
        y_pred (torch.Tensor): Predicted labels or raw model outputs for the images.
        images (torch.Tensor): Batch of images to display.
        output_path (str, optional): Path to save the plot. If None, shows the plot.
                                     Defaults to None.
    """
    fig, axs = plt.subplots(2, 10, figsize=(15, 4))

    for i, a_x in enumerate(axs.flatten()):
        image_tensor = torch.squeeze(images[i, 0, :, :]).detach()
        a_x.imshow(image_tensor, vmin=-1, vmax=1, cmap="jet")
        true_label = int(y_true[i].item())
        pred_label = int(y_pred[i].item() > 0.5)
        a_x.set_title(f"T:{true_label}, P:{pred_label}")
        a_x.set_xticks([])
        a_x.set_yticks([])

    if output_path:
        plt.savefig(output_path)
        plt.close(fig)
    else:
        plt.show()


def _calculate_feature_map_correlations(feature_maps):
    """
    Calculate correlation data from feature maps.

    Args:
        feature_maps (torch.Tensor): A tensor of feature maps.

    Returns:
        tuple: A tuple containing:
            - allrs (np.ndarray): All correlation values.
            - call (np.ndarray): Sum of correlation matrices.
            - xlab (list): Labels for the x-axis.
            - number_stimulus (int): Number of stimuli (images).
            - number_correlations (int): Number of unique correlations.
    """
    number_stimulus = feature_maps.shape[0]
    number_maps = feature_maps.shape[1]
    number_correlations = (number_maps * (number_maps - 1)) // 2

    allrs = np.zeros((number_stimulus, number_correlations))
    call = np.zeros((number_maps, number_maps))
    idx = None  # To hold indices of upper triangle

    for i in range(number_stimulus):
        feature_maps_s = feature_maps[i, :, :, :].view(number_maps, -1).detach()
        correlation_matrix = np.corrcoef(feature_maps_s)
        call += correlation_matrix
        idx = np.nonzero(np.triu(correlation_matrix, 1))
        allrs[i, :] = correlation_matrix[idx]

    xlab = []
    if idx is not None:
        for i in range(number_correlations):
            xlab.append(f"{idx[0][i]}-{idx[1][i]}")

    return allrs, call, xlab, number_stimulus, number_correlations


def plot_correlation_values(feature_maps, output_path=None):
    """
    Visualizes the correlation coefficients between feature maps for each image
    and the average correlation matrix across all images.

    Args:
        feature_maps (torch.Tensor): A tensor of feature maps, typically from a convolutional layer,
                                     with shape (num_stimulus, num_maps, height, width).
        output_path (str, optional): Path to save the plot. If None, shows the plot.
                                     Defaults to None.
    """
    (
        allrs,
        call,
        xlab,
        num_stimulus,
        num_correlations,
    ) = _calculate_feature_map_correlations(feature_maps)

    # now visualize the correlations
    fig = plt.figure(figsize=(14, 5))
    ax0 = fig.add_axes([0.1, 0.1, 0.55, 0.9])  # [left, bottom, width, height]
    ax1 = fig.add_axes([0.68, 0.1, 0.3, 0.9])
    cax = fig.add_axes([0.99, 0.1, 0.01, 0.9])

    for i in range(num_correlations):
        ax0.plot(
            i + np.random.randn(num_stimulus) / 30,
            allrs[:, i],
            "o",
            markerfacecolor="w",
            markersize=10,
        )

    # make the plot more interpretable
    ax0.set_xlim([-0.5, num_correlations - 0.5])
    ax0.set_ylim([-1.05, 1.05])
    ax0.set_xticks(range(num_correlations))
    ax0.set_xticklabels(xlab)
    ax0.set_xlabel("Feature map pair")
    ax0.set_ylabel("Correlation coefficient")
    ax0.set_title("Correlations for each image")

    # now show the average correlation matrix
    h = ax1.imshow(call / num_stimulus, vmin=-1, vmax=1)
    ax1.set_title("Correlation matrix")
    ax1.set_xlabel("Feature map")
    ax1.set_ylabel("Feature map")
    # add a colorbar
    fig.colorbar(h, cax=cax)

    if output_path:
        plt.savefig(output_path)
        plt.close(fig)
    else:
        plt.show()


def plot_feature_maps(feature_maps, images, true_labels, output_path=None):
    """
    Displays original images and their corresponding feature map activations from a model layer.

    Args:
        feature_maps (torch.Tensor): Feature maps from a model layer,
                                     shape (batch_size, num_feature_maps, height, width).
        images (torch.Tensor): Original input images, shape (batch_size, channels, height, width).
        true_labels (torch.Tensor): True labels for the images.
        output_path (str, optional): Path to save the plot. If None, shows the plot.
                                     Defaults to None.
    """
    # Draw the feature maps
    # Feature maps from the conv1 layer

    fig, axs = plt.subplots(feature_maps.shape[1] + 1, 10, figsize=(12, 6))

    for pici in range(10):

        # show the original picture
        img = images[pici, 0, :, :].detach()
        axs[0, pici].imshow(img, cmap="jet", vmin=0, vmax=1)
        axs[0, pici].axis("off")
        axs[0, pici].text(
            2,
            2,
            f"T:{int(true_labels[pici].item())}",
            ha="left",
            va="top",
            color="w",
            fontweight="bold",
        )

        for feati in range(feature_maps.shape[1]):
            # extract the feature map from this image
            img = feature_maps[pici, feati, :, :].detach()
            axs[feati + 1, pici].imshow(
                img, cmap="inferno", vmin=0, vmax=torch.max(img) * 0.9
            )
            axs[feati + 1, pici].axis("off")
            if pici == 0:
                axs[feati + 1, pici].text(
                    -5, feature_maps.shape[2] / 2, feati, ha="right"
                )

    plt.tight_layout()
    plt.suptitle("Set of feature map activations for 10 test images", x=0.5, y=1.01)

    if output_path:
        plt.savefig(output_path)
        plt.close(
            fig
        )  # Assuming fig is defined earlier, which it is implicitly by subplots
    else:
        plt.show()
