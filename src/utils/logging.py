"""
Logging utilities for the coiny-classifier project.

This module provides a consistent logging interface with graceful degradation
when logging configuration is not available.
"""

import logging
import os
import sys
from pathlib import Path


def get_logger(name, log_level=None, log_file=None):
    """
    Get a logger with the specified name and configuration.

    Args:
        name (str): Name for the logger, typically __name__ of the calling module
        log_level (int, optional): Logging level (e.g., logging.INFO). Defaults to INFO or env var.
        log_file (str, optional): Path to log file. If None, logs to console only.

    Returns:
        logging.Logger: Configured logger instance
    """
    # Get log level from environment or use default
    if log_level is None:
        log_level_str = os.environ.get("COINY_LOG_LEVEL", "INFO")
        log_level = getattr(logging, log_level_str, logging.INFO)

    # Create logger
    logger = logging.getLogger(name)
    logger.setLevel(log_level)

    # Clear existing handlers to avoid duplicates
    if logger.hasHandlers():
        logger.handlers.clear()

    # Create formatter
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )

    # Add console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # Add file handler if log_file is specified
    if log_file:
        try:
            # Create directory if it doesn't exist
            log_dir = os.path.dirname(log_file)
            if log_dir:
                Path(log_dir).mkdir(parents=True, exist_ok=True)

            file_handler = logging.FileHandler(log_file)
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        except (IOError, PermissionError) as e:
            # Graceful degradation - log to console that we couldn't set up file logging
            logger.warning("Could not set up log file at %s: %s", log_file, str(e))

    return logger


def setup_training_logger(run_output_dir, model_id):
    """
    Set up a logger specifically for model training.

    Args:
        run_output_dir (str): Directory where training artifacts are saved
        model_id (str): Identifier for the model run

    Returns:
        logging.Logger: Configured logger for training
    """
    log_dir = Path(run_output_dir) / "logs"
    log_file = log_dir / f"{model_id}_training.log"

    return get_logger(f"training.{model_id}", log_file=str(log_file))
