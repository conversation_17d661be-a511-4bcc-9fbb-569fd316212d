"""
This module provides utility functions for loading and generating datasets for model training.
"""

import numpy as np
import torch
from sklearn.model_selection import train_test_split
from torch.utils.data import DataLoader, TensorDataset


def data_loader(images, labels, batch_size=32, test_size=0.1):
    """
    Function to separate date into train and test datasets using DataLoader

    Parameters:
    images (list): A list of data points.
    labels (list): A list of data points.
    batch_size (Int): An integer representing the batch size.
    test_size (Float): Float between 0 and 1 representing the

    Returns:
    train_loader: Train data wrapped in a dataloader
    test_loader: Test data wrapped in a dataloader.
    """
    # Create train/test groups using DataLoader
    # Step 2: use scikitlearn to split the data
    train_data, test_data, train_labels, test_labels = train_test_split(
        images, labels, test_size=test_size
    )

    # Step 3: convert into PyTorch Datasets
    train_data = TensorDataset(train_data, train_labels)
    test_data = TensorDataset(test_data, test_labels)

    # Step 4: translate into dataloader objects
    train_loader = DataLoader(
        train_data, batch_size=batch_size, shuffle=True, drop_last=True
    )
    test_loader = DataLoader(test_data, batch_size=test_data.tensors[0].shape[0])

    return train_loader, test_loader


def generate_gaussian_blurs(image_size=10, number_per_class=10):
    """
    Function to preprocess data for training

    Parameters:
    images (list): A list of data points.
    labels (list): A list of data points.

    Returns:
    images: Preprocessed images
    """
    x = np.linspace(-4, 4, image_size)
    X, Y = np.meshgrid(x, x)

    # the two widths (a.u.)
    widths = [1.8, 2.4]

    # initialize tensors containing images and labels
    images = torch.zeros(2 * number_per_class, 1, image_size, image_size)
    labels = torch.zeros(2 * number_per_class)

    for i in range(2 * number_per_class):

        # create the gaussian with random centers
        ro = 2 * np.random.randn(2)  # ro = random offset
        gaussian_kernel = np.exp(
            -((X - ro[0]) ** 2 + (Y - ro[1]) ** 2) / (2 * widths[i % 2] ** 2)
        )

        # and add noise
        gaussian_kernel = gaussian_kernel + np.random.randn(image_size, image_size) / 5

        # add to the tensor
        images[i, :, :, :] = torch.Tensor(gaussian_kernel).view(
            1, image_size, image_size
        )
        labels[i] = i % 2

    labels = labels[:, None]
    return images, labels
