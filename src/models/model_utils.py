"""
Utility functions for model creation and manipulation.

This module provides helper functions used across different model-related
modules to promote code reuse and single responsibility.
"""

from typing import Any, Dict, Optional, Type

from torch import nn, optim
from torch.optim import lr_scheduler


LOSS_FUNCTIONS: Dict[str, Type[nn.Module]] = {
    "bce": nn.BCEWithLogitsLoss,
    "ce": nn.CrossEntropyLoss,
    "mse": nn.MSELoss,
}

OPTIMIZERS: Dict[str, Type[optim.Optimizer]] = {
    "adam": optim.<PERSON>,
    "adamw": optim.AdamW,
    "sgd": optim.SGD,
}

SCHEDULERS: Dict[str, Type[lr_scheduler._LRScheduler]] = {
    "step": lr_scheduler.StepLR,
    "cosine": lr_scheduler.CosineAnnealingLR,
    "plateau": lr_scheduler.ReduceLROnPlateau,
}


def get_activation(activation: Optional[str]) -> nn.Module:
    """Returns an activation function module based on the name provided."""
    if activation is None or activation.lower() == "relu":
        return nn.ReLU(inplace=True)
    if activation.lower() == "leakyrelu":
        return nn.LeakyReLU(0.2, inplace=True)
    # Add other activations as needed, e.g., 'sigmoid', 'tanh'
    raise ValueError(f"Unsupported activation function: {activation}")


def create_loss_function(loss_params: Dict[str, Any]) -> nn.Module:
    """Creates a loss function based on the provided parameters."""
    loss_type = loss_params.get("type", "bce").lower()
    if loss_type not in LOSS_FUNCTIONS:
        raise ValueError(f"Unsupported loss function: {loss_type}")

    loss_class = LOSS_FUNCTIONS[loss_type]
    return loss_class()


def create_optimizer(
    optimizer_params: Dict[str, Any], model_parameters
) -> optim.Optimizer:
    """Creates an optimizer based on the provided parameters."""
    opt_type = optimizer_params.get("type", "adam").lower()
    if opt_type not in OPTIMIZERS:
        raise ValueError(f"Unsupported optimizer: {opt_type}")

    lr = float(optimizer_params.get("learning_rate", 1e-3))
    optimizer_class = OPTIMIZERS[opt_type]
    return optimizer_class(model_parameters, lr=lr)


def create_scheduler(
    scheduler_params: Dict[str, Any], optimizer: optim.Optimizer
) -> Optional[lr_scheduler._LRScheduler]:
    """Creates a learning rate scheduler based on the provided parameters."""
    sched_type = scheduler_params.get("type")
    if not sched_type:
        return None

    sched_type = sched_type.lower()
    if sched_type not in SCHEDULERS:
        raise ValueError(f"Unsupported scheduler: {sched_type}")

    scheduler_class = SCHEDULERS[sched_type]
    scheduler_args = {k: v for k, v in scheduler_params.items() if k != "type"}
    return scheduler_class(optimizer, **scheduler_args)
