"""
Model persistence module for saving and loading trained models.

This module provides functionality for general-purpose model storage and retrieval
operations, separate from training-specific checkpoint management.
"""

import json
import shutil
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

import torch
from torch import nn, optim


@dataclass
class ModelData:
    """Data class for model persistence parameters."""

    model: nn.Module
    optimizer: Optional[optim.Optimizer] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class LoadModelOptions:
    """Data class for model loading options."""

    model: nn.Module
    optimizer: Optional[optim.Optimizer] = None
    device: Union[str, torch.device] = "cpu"


class ModelPersistence:
    """
    Handles saving, loading, and managing trained models.

    This class provides general-purpose functionality for model storage and retrieval,
    separate from training-specific checkpoint management.
    """

    def __init__(self, base_dir: Optional[Union[str, Path]] = None):
        """
        Initialize the model persistence manager.

        Args:
            base_dir: Base directory for model storage.
                If None, uses 'runs' in the project root.
        """
        if base_dir is None:
            # Assumes the project root is two levels up from this file's location
            project_root = Path(__file__).resolve().parent.parent.parent
            base_dir = project_root / "runs"
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(parents=True, exist_ok=True)

    def save_model(self, data: ModelData, model_id: str) -> str:
        """Save a model checkpoint to disk.

        Args:
            data: ModelData containing model, optimizer, and metadata
            model_id: Unique identifier for the model

        Returns:
            Path to the saved checkpoint
        """
        model_dir = self.base_dir / model_id
        model_dir.mkdir(parents=True, exist_ok=True)

        checkpoint = {
            "model_state_dict": data.model.state_dict(),
            "metadata": data.metadata or {},
        }

        if data.optimizer:
            checkpoint["optimizer_state_dict"] = data.optimizer.state_dict()

        checkpoint_path = model_dir / "checkpoint.pt"
        torch.save(checkpoint, checkpoint_path)

        # Also save metadata separately for easy inspection
        if data.metadata:
            metadata_path = model_dir / "metadata.json"
            with metadata_path.open("w") as f:
                json.dump(data.metadata, f, indent=2, default=str)

        return str(model_dir)

    def load_model(
        self, options: LoadModelOptions, model_id: str
    ) -> Tuple[nn.Module, Optional[optim.Optimizer], Dict[str, Any]]:
        """Load a model checkpoint from disk.

        Args:
            options: LoadModelOptions containing model and optimizer to load into
            model_id: Unique identifier for the model

        Returns:
            Tuple of (model, optimizer, metadata)

        Raises:
            FileNotFoundError: If the model checkpoint does not exist
        """
        model_dir = self.base_dir / model_id
        checkpoint_path = model_dir / "checkpoint.pt"

        if not checkpoint_path.exists():
            raise FileNotFoundError(f"Checkpoint not found at {checkpoint_path}")

        checkpoint = torch.load(checkpoint_path, map_location=options.device)

        options.model.load_state_dict(checkpoint["model_state_dict"])
        options.model.to(options.device)

        optimizer = None
        if options.optimizer and "optimizer_state_dict" in checkpoint:
            options.optimizer.load_state_dict(checkpoint["optimizer_state_dict"])
            optimizer = options.optimizer

        return options.model, optimizer, checkpoint.get("metadata", {})

    def discard_model(self, model_id: str) -> bool:
        """
        Discard (delete) a model and all its associated files.

        Args:
            model_id: Unique identifier for the model

        Returns:
            True if model was successfully deleted, False otherwise
        """
        model_dir = self.base_dir / model_id
        if not model_dir.exists():
            return False
        try:
            shutil.rmtree(model_dir)
            return True
        except OSError:
            return False

    def list_models(self) -> List[str]:
        """
        List all available models.

        Returns:
            List of model IDs
        """
        if not self.base_dir.exists():
            return []
        return [d.name for d in self.base_dir.iterdir() if d.is_dir()]
