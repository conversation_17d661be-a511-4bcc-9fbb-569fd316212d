"""
ML Model Factory for creating, configuring, and managing PyTorch models.
"""

from pathlib import Path
from typing import Any, Dict, Optional, Union

from torch import nn, optim
from torch.optim import lr_scheduler

from .architectures import create_model_from_architecture
from .dynamic_cnn import create_dynamic_cnn
from .model_utils import create_loss_function as create_loss_function_util
from .model_utils import create_optimizer as create_optimizer_util
from .model_utils import create_scheduler as create_scheduler_util
from .persistence import ModelPersistence


class MLModelFactory:
    """
    Factory for creating and configuring ML models, loss functions, and optimizers.
    """

    def __init__(self, persistence_base_dir: Optional[Union[str, Path]] = None):
        self.persistence = ModelPersistence(base_dir=persistence_base_dir)

    def create_model(
        self, architecture_params: Dict[str, Any], num_classes: int = 1
    ) -> nn.Module:
        """
        Create a model based on architecture parameters.
        """
        arch_name = architecture_params.get("name")
        if not arch_name:
            raise ValueError("Architecture 'name' must be specified.")

        variant = architecture_params.get("variant", "")
        full_arch_name = f"{arch_name}{variant}"

        if arch_name.upper() == "CNN":
            # Extract nested parameters for the dynamic CNN builder
            nested_params = architecture_params.get("parameters", {})

            model_version_params = (nested_params.get("model_version") or {}).get(
                "parameters", {}
            )
            model_run_params = (nested_params.get("model_run") or {}).get(
                "parameters", {}
            )

            # Corrected image_size and image_channels retrieval
            current_image_size = nested_params.get("image_size", 91)
            current_image_channels = nested_params.get("image_channels", 1)

            model, _, _ = create_dynamic_cnn(
                {
                    "model_params": model_version_params,
                    "dropout_rate": model_run_params.get("dropout_rate"),
                    "num_classes": num_classes,
                    "image_size": current_image_size,
                    "image_channels": current_image_channels,
                }
            )
            return model

        try:
            model = create_model_from_architecture(
                architecture_name=full_arch_name,
                num_classes=num_classes,
                pretrained=architecture_params.get("pretrained", False),
            )
            return model
        except (ValueError, NotImplementedError) as e:
            raise ValueError(f"Failed to create model '{full_arch_name}': {e}") from e

    def create_loss_function(self, loss_params: Dict[str, Any]) -> nn.Module:
        """
        Create a loss function.
        """
        return create_loss_function_util(loss_params)

    def create_optimizer(
        self, optimizer_params: Dict[str, Any], model_parameters
    ) -> optim.Optimizer:
        """
        Create an optimizer.
        """
        return create_optimizer_util(optimizer_params, model_parameters)

    def create_scheduler(
        self, scheduler_params: Dict[str, Any], optimizer: optim.Optimizer
    ) -> Optional[lr_scheduler._LRScheduler]:
        """
        Create a learning rate scheduler.
        """
        return create_scheduler_util(scheduler_params, optimizer)
