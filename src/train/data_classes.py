"""
Data classes used for training, including configuration, metrics, and data loaders.
"""

from collections import defaultdict
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional

import torch


@dataclass
# pylint: disable=too-many-instance-attributes
class TrainingConfig:
    """Configuration for a training run."""

    config: Dict[str, Any]
    learning_rate_schedule: Optional[List[Dict[str, Any]]] = field(
        init=False, default=None
    )
    gradient_clip_max_norm: Optional[float] = field(init=False, default=None)

    def __post_init__(self):
        """
        Sets attributes from the config dict with defaults.
        """
        self.run_output_dir: str = self.config.get("run_output_dir", "output")
        self.model_id: str = self.config.get("model_id", "model")
        self.epochs: int = self.config.get("epochs", 10)

        self.learning_rate_schedule: Optional[List[Dict[str, Any]]] = self.config.get(
            "learning_rate_schedule", None
        )
        self.gradient_clip_max_norm: Optional[float] = self.config.get(
            "gradient_clip_max_norm", None
        )

    def __getattr__(self, name: str) -> Any:
        """
        Fallback to getting from the original config dict.
        """
        if name in self.config:
            return self.config[name]
        raise AttributeError(f"'TrainingConfig' object has no attribute '{name}'")


@dataclass
class TrainingComponents:
    """Core components for training."""

    model: torch.nn.Module
    loss_fn: Any
    optimizer: torch.optim.Optimizer


@dataclass
# pylint: disable=too-many-instance-attributes
class TrainingMetrics:
    """
    A dataclass to store training metrics.
    """

    epoch_metrics: List[Dict[str, Any]] = field(default_factory=list)
    batch_metrics: List[Dict[str, Any]] = field(default_factory=list)
    train_losses: List[float] = field(default_factory=list)
    train_accuracies: List[float] = field(default_factory=list)
    test_losses: List[float] = field(default_factory=list)
    test_accuracies: List[float] = field(default_factory=list)
    timing: Dict[str, List[float]] = field(default_factory=lambda: defaultdict(list))
    resources: Dict[str, List[float]] = field(default_factory=lambda: defaultdict(list))
    custom_metrics: Dict[str, Any] = field(default_factory=dict)
    error: Optional[Dict[str, str]] = None

    def __post_init__(self):
        # Ensure timing and resources are defaultdicts even if initialized from a dict
        if not isinstance(self.timing, defaultdict):
            self.timing = defaultdict(list, self.timing or {})
        if not isinstance(self.resources, defaultdict):
            self.resources = defaultdict(list, self.resources or {})

    def get_metric_summary(self) -> Dict[str, Any]:
        """Returns a summary dictionary of key metrics."""
        summary = {}
        if self.train_losses:
            summary["final_train_loss"] = self.train_losses[-1]
        if self.train_accuracies:
            summary["final_train_accuracy"] = self.train_accuracies[-1]
        if self.test_losses:
            summary["final_test_loss"] = self.test_losses[-1]
        if self.test_accuracies:
            summary["final_test_accuracy"] = self.test_accuracies[-1]

        if self.timing:
            summary["timing"] = dict(
                self.timing
            )  # Convert defaultdict to dict for cleaner JSON

        if self.error:
            summary["error"] = self.error

        summary["resources"] = dict(
            self.resources
        )  # Convert defaultdict to dict for cleaner JSON

        # Optionally, include custom metrics in the summary if they are simple values
        # For now, keeping it to the most common summary items.
        # summary["custom_metrics"] = {
        #     k: v[-1] if isinstance(v, list) and v
        #       else v for k, v in self.custom_metrics.items()
        # }
        return summary

    def get(self, key: str, default: Any = None) -> Any:
        """Get a metric by key, checking top-level attributes and custom_metrics."""
        if hasattr(self, key):
            value = getattr(self, key)
            # Prevent returning methods if a field has the same name as a method by chance
            if not callable(value):
                return value

        if key in self.custom_metrics:
            return self.custom_metrics[key]

        # Check common nested dicts if not found at top level or in custom_metrics
        for dict_name in ["timing", "resources"]:
            nested_dict = getattr(self, dict_name, None)
            if isinstance(nested_dict, dict) and key in nested_dict:
                return nested_dict[key]

        return default


@dataclass
class EarlyStoppingState:
    """State for early stopping logic."""

    counter: int = 0
    best_metric_value: float = float("inf")
    best_epoch: int = 0
    stopped_epoch: int = 0
    active: bool = True  # To enable/disable early stopping if needed
