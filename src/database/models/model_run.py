"""
ModelRun class definition.
"""

from datetime import datetime
from typing import Dict, List, Optional, Union
from uuid import UUID

from pydantic import BaseModel, Field, field_validator


class OptimiserConfig(BaseModel):
    """Optimiser configuration parameters."""

    # Common parameters
    learning_rate: Optional[float] = Field(
        None, description="Learning rate for optimization"
    )

    # SGD specific parameters
    momentum: Optional[float] = Field(None, description="Momentum for SGD")
    decay: Optional[float] = Field(None, description="Weight decay/L2 regularization")

    # Adam/AdamW specific parameters
    beta1: Optional[float] = Field(
        None, description="Exponential decay rate for first moment"
    )
    beta2: Optional[float] = Field(
        None, description="Exponential decay rate for second moment"
    )
    epsilon: Optional[float] = Field(
        None, description="Small constant for numerical stability"
    )

    # RMSProp specific parameters
    rho: Optional[float] = Field(None, description="Discounting factor for gradients")

    class Config:
        """Configuration for API documentation."""

        json_schema_extra = {
            "example": {
                "learning_rate": 0.001,
                "momentum": 0.9,
                "decay": 1e-6,
                "beta1": 0.9,
                "beta2": 0.999,
                "epsilon": 1e-8,
                "rho": 0.9,
            }
        }


class LearningRateSchedule(BaseModel):
    """Learning rate schedule configuration."""

    rate: float = Field(..., description="Learning rate value")
    epoch: int = Field(..., description="Epoch number when this rate applies")

    class Config:  # pylint: disable=too-few-public-methods
        """Configuration for API documentation."""

        json_schema_extra = {"example": {"rate": 0.001, "epoch": 10}}


class AugmentationItem(BaseModel):
    """Base class for augmentation items."""


# This will be defined at the end of the file


class HorizontalFlip(AugmentationItem):
    """Horizontal flip augmentation."""

    horizontal_flip: bool


class RotationRange(AugmentationItem):
    """Rotation range augmentation."""

    rotation_range: float


class ZoomRange(AugmentationItem):
    """Zoom range augmentation."""

    zoom_range: float


class ResizeDimensions(AugmentationItem):
    """Resize dimensions augmentation."""

    resize_dimensions: List[int]

    @field_validator("resize_dimensions")
    @classmethod
    def validate_dimensions(cls, v):
        """Validate that resize_dimensions is a list of 2 integers."""
        if not (
            isinstance(v, list) and len(v) == 2 and all(isinstance(x, int) for x in v)
        ):
            raise ValueError("resize_dimensions must be a list of 2 integers")
        return v


class NormalizationMean(AugmentationItem):
    """Normalization mean augmentation."""

    normalization_mean: List[float]

    @field_validator("normalization_mean")
    @classmethod
    def validate_mean(cls, v):
        """Validate that normalization_mean is a list of 3 numbers."""
        if not (
            isinstance(v, list)
            and len(v) == 3
            and all(isinstance(x, (int, float)) for x in v)
        ):
            raise ValueError("normalization_mean must be a list of 3 numbers")
        return v


class NormalizationStd(AugmentationItem):
    """Normalization standard deviation augmentation."""

    normalization_std: List[float]

    @field_validator("normalization_std")
    @classmethod
    def validate_std(cls, v):
        """Validate that normalization_std is a list of 3 numbers."""
        if not (
            isinstance(v, list)
            and len(v) == 3
            and all(isinstance(x, (int, float)) for x in v)
        ):
            raise ValueError("normalization_std must be a list of 3 numbers")
        return v


class ModelRunParameters(BaseModel):
    """Training run parameters configuration."""

    # Hyperparameters
    learning_rate: float = Field(..., description="Learning rate for training")
    batch_size: int = Field(..., description="Batch size for training")
    optimiser_config: OptimiserConfig = Field(
        ..., description="Optimiser configuration"
    )

    # Training specifics
    epochs: int = Field(..., description="Number of training epochs")
    dropout_rate: Optional[float] = Field(None, description="Dropout rate")
    random_seed: Optional[int] = Field(
        None, description="Random seed for reproducibility"
    )

    # Learning rate schedule
    learning_rate_schedule: Optional[List[LearningRateSchedule]] = Field(
        None, description="List of learning rate values and their corresponding epochs"
    )

    # Model specific
    confidence_threshold: float = Field(
        default=0.7, description="Confidence threshold for predictions"
    )
    gradient_clip_max_norm: Optional[float] = Field(
        None, gt=0, description=("Max norm for gradient clipping. If None/0, disabled.")
    )

    class Config:
        """Configuration for API documentation."""

        json_schema_extra = {
            "example": {
                "learning_rate": 0.001,
                "batch_size": 32,
                "optimiser_config": OptimiserConfig.Config.json_schema_extra["example"],
                "epochs": 100,
                "dropout_rate": 0.5,
                "random_seed": 42,
                "learning_rate_schedule": [
                    LearningRateSchedule.Config.json_schema_extra["example"],
                    {"rate": 0.0001, "epoch": 20},
                ],
                "confidence_threshold": 0.7,
                "gradient_clip_max_norm": 1.0,
            }
        }


class ModelRun(BaseModel):
    """Model run representing a training or testing session."""

    uuid: str | UUID = Field(default=None, description="Unique identifier")
    created_at: Optional[datetime] = Field(
        default=None, description="Timestamp of creation"
    )
    is_training: Optional[bool] = Field(
        default=False, description="Indicates if this is a training run"
    )
    is_testing: Optional[bool] = Field(
        default=False, description="Indicates if this is a testing run"
    )
    model_version_uuid: Optional[str | UUID] = Field(
        None, description="UUID of the model version"
    )
    dataset_uuid: Optional[str | UUID] = Field(None, description="UUID of the dataset")
    schedule_time: datetime = Field(..., description="Scheduled time for the run")
    prepared_time: Optional[datetime] = Field(
        None, description="Actual time when the run data was prepared"
    )
    start_time: Optional[datetime] = Field(
        None, description="Actual start time of the run"
    )
    end_time: Optional[datetime] = Field(None, description="End time of the run")
    metrics: Optional[Dict] = Field(None, description="Run metrics and results")
    log_path: Optional[bool] = Field(
        None, description="Indicates if logs are available"
    )
    model_artifact_path: Optional[str] = Field(
        None, description="Path to model artifacts"
    )
    parameters: Dict = Field(..., description="Training parameters")
    augmentations: Optional[
        List[
            Union[
                HorizontalFlip,
                RotationRange,
                ZoomRange,
                ResizeDimensions,
                NormalizationMean,
                NormalizationStd,
            ]
        ]
    ] = Field(None, description="List of augmentation parameters to apply in order")
    environment_info: Optional[str] = Field(
        None, description="Information about the runtime environment"
    )
    experiment_uuid: Optional[str | UUID] = Field(
        None, description="UUID of the associated experiment"
    )
    note: Optional[str] = Field(None, description="Additional notes about the run")
    dataset_content_updated_at: Optional[datetime] = Field(
        None, description="Timestamp when dataset content was last updated"
    )

    class Config:
        """Configuration for API documentation."""

        json_schema_extra = {
            "example": {
                "uuid": "031dafbc-7a8e-466e-bf7e-867ebd3325c1",
                "created_at": "2023-10-01T12:00:00Z",
                "is_training": True,
                "is_testing": False,
                "model_version_uuid": "some-model-version-uuid",
                "dataset_uuid": "some-dataset-uuid",
                "schedule_time": "2023-10-02T12:00:00Z",
                "prepared_time": "2023-10-02T12:15:00Z",
                "start_time": "2023-10-01T12:30:00Z",
                "end_time": None,
                "metrics": {"accuracy": 0.95, "loss": 0.05},
                "log_path": True,
                "model_artifact_path": "/artifacts/model_run_031dafbc.zip",
                "parameters": {"learning_rate": 0.001, "batch_size": 64},
                "augmentations": [
                    {"horizontal_flip": True},
                    {"rotation_range": 15.0},
                    {"normalization_mean": [0.485, 0.456, 0.406]},
                ],
                "environment_info": "Python 3.8, TensorFlow 2.4",
                "experiment_uuid": "some-experiment-uuid",
                "note": "Initial run with baseline parameters.",
                "dataset_content_updated_at": "2023-09-30T12:00:00Z",
            }
        }


# Define the AugmentationItem type as a Union of all augmentation types
AugmentationItemType = Union[
    HorizontalFlip,
    RotationRange,
    ZoomRange,
    ResizeDimensions,
    NormalizationMean,
    NormalizationStd,
]
