"""
ModelVersion class definition.
"""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field


class ModelVersionParameters(BaseModel):
    """Model parameters configuration."""

    # Neural network activation and initialization
    activation: Optional[str] = Field(None, description="Activation function type")
    weight_init: Optional[str] = Field(
        None, description="Weight initialization strategy"
    )

    # Architecture components
    residual_blocks: Optional[int] = Field(
        None, description="Number of residual blocks in the model"
    )
    dense_blocks: Optional[int] = Field(
        None, description="Number of dense blocks in the model"
    )
    transition_layers: Optional[int] = Field(
        None, description="Number of transition layers between dense blocks"
    )
    layers_number: Optional[int] = Field(None, description="Total number of layers")

    # Encoder-decoder and attention mechanisms
    encoder_decoder: Optional[str] = Field(
        None, description="Encoder-decoder architecture type"
    )
    embedding_dimension: Optional[int] = Field(
        None, description="Dimension of embeddings"
    )
    attention_heads: Optional[int] = Field(
        None, description="Number of attention heads"
    )
    auxiliary_classifier: Optional[str] = Field(
        None, description="Type of auxiliary classifier"
    )

    # Convolution configurations
    kernel_size: Optional[List[int]] = Field(
        None, description="Kernel dimensions [height, width]"
    )
    stride: Optional[List[int]] = Field(
        None, description="Stride values for convolutions"
    )
    convolutional_layers: Optional[List[List[int]]] = Field(
        None,
        description="Conv layer configs [kernelSizeX, kernelSizeY, stride]",
    )
    depth_separable_convolutions: Optional[List[List[int]]] = Field(
        None, description="Depth separable convolution configurations"
    )
    inception_modules: Optional[List[List[int]]] = Field(
        None, description="Inception module configurations"
    )

    # Normalization and regularization
    batch_norm: Optional[int] = Field(None, description="Batch normalization parameter")
    regularisation: Optional[float] = Field(None, description="Regularization strength")

    # Pooling and spatial operations
    pooling: Optional[List[int]] = Field(
        None, description="Pooling configuration [type, size, stride]"
    )
    upsampling: Optional[int] = Field(None, description="Upsampling factor")
    grid_size: Optional[int] = Field(None, description="Grid size for detection models")

    # Scaling factors
    resolution_multiplier: Optional[float] = Field(
        None, description="Resolution scaling factor"
    )
    width_multiplier: Optional[float] = Field(None, description="Width scaling factor")
    compound_scaling: Optional[List[float]] = Field(
        None, description="Compound scaling factors [depth, width, resolution]"
    )
    growth_rate: Optional[int] = Field(
        None, description="Growth rate for dense networks"
    )

    # Input and output configurations
    input_size: Optional[List[int]] = Field(
        None, description="Input dimensions [height, width, channels]"
    )
    patch_size: Optional[List[int]] = Field(
        None, description="Patch dimensions [height, width] for vision transformers"
    )
    fully_connected_layers: Optional[List[int]] = Field(
        None, description="Fully connected layer dimensions [input, output]"
    )

    # Detection specific parameters
    anchor_boxes: Optional[int] = Field(None, description="Number of anchor boxes")
    bounding_boxes: Optional[int] = Field(None, description="Number of bounding boxes")

    # Training specific parameters
    optimiser: Optional[str] = Field(None, description="Optimization algorithm")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "activation": "relu",
                "weight_init": "kaiming_normal",
                "residual_blocks": 16,
                "dense_blocks": 4,
                "transition_layers": 3,
                "layers_number": 50,
                "encoder_decoder": "transformer",
                "embedding_dimension": 768,
                "attention_heads": 8,
                "auxiliary_classifier": "attention",
                "kernel_size": [3, 3],
                "stride": [2, 2],
                "convolutional_layers": [[3, 3, 1], [5, 5, 2]],
                "depth_separable_convolutions": [[3, 1], [5, 2]],
                "inception_modules": [[1, 64], [3, 96]],
                "batch_norm": 1,
                "regularisation": 0.001,
                "pooling": [0, 2, 2],
                "upsampling": 2,
                "grid_size": 16,
                "resolution_multiplier": 1.5,
                "width_multiplier": 1.0,
                "compound_scaling": [1.2, 1.1, 1.15],
                "growth_rate": 32,
                "input_size": [224, 224, 3],
                "patch_size": [16, 16],
                "fully_connected_layers": [1024, 512],
                "anchor_boxes": 9,
                "bounding_boxes": 4,
                "optimiser": "adam",
            }
        }
    )


class ModelVersion(BaseModel):
    """Model version representing a specific version of a trained model."""

    uuid: str | UUID = Field(default=None, description="Unique identifier")
    model_uuid: str | UUID | None = Field(None, description="UUID of the parent model")
    version_number: int = Field(..., description="Version number of the model")
    parameters: Optional[ModelVersionParameters] = Field(
        None, description="Model parameters configuration"
    )
    created_at: Optional[datetime] = Field(
        default=None, description="Timestamp of creation"
    )
    user_id: str | UUID | None = Field(
        None, description="ID of the user who owns the model version"
    )
    name: Optional[str] = Field(None, description="Name of the model version")
    note: Optional[str] = Field(
        None, description="Additional notes about the model version"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "uuid": "bbf27ea7-9998-490d-b7da-6be47522fe4e",
                "model_uuid": "123e4567-e89b-12d3-a456-************",
                "version_number": 1,
                "parameters": {"learning_rate": 0.01, "batch_size": 32, "epochs": 10},
                "created_at": "2023-10-01T12:00:00Z",
                "user_id": "98765432-e89b-12d3-a456-************",
                "name": "Initial Model Version",
                "note": "This is the first version of the model.",
            }
        }
    )
