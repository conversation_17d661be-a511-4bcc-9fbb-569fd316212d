"""
File utility functions for handling dataset files.
"""

import asyncio
import os
from pathlib import Path
from typing import Op<PERSON>, <PERSON>ple
from urllib.parse import urlparse
import aiohttp
import aiofiles


async def download_file(
    url: str,
    output_dir: Path,
    filename: Optional[str] = None,
    session: Optional[aiohttp.ClientSession] = None,
    timeout: int = 300,
) -> Tuple[bool, str]:
    """
    Download a file from a URL to the specified directory.

    Args:
        url: URL of the file to download
        output_dir: Directory to save the downloaded file
        filename: Optional custom filename (uses original filename if not provided)
        session: Optional aiohttp ClientSession for connection pooling
        timeout: Request timeout in seconds

    Returns:
        Tuple of (success: bool, message: str)
    """
    if not filename:
        # Extract filename from URL if not provided
        parsed_url = urlparse(url)
        filename = os.path.basename(parsed_url.path)
        if not filename:
            return False, f"Could not determine filename from URL: {url}"

    # Ensure output directory exists
    output_dir.mkdir(parents=True, exist_ok=True)
    output_path = output_dir / filename

    # Skip if file already exists
    if output_path.exists():
        return True, f"File already exists: {output_path}"

    # Create a new session if one wasn't provided
    close_session = False
    if session is None:
        session = aiohttp.ClientSession()
        close_session = True

    try:
        async with session.get(url, timeout=timeout) as response:
            if response.status != 200:
                return False, f"Failed to download {url}: HTTP {response.status}"

            # Stream the download to handle large files
            async with aiofiles.open(output_path, "wb") as f:
                async for chunk in response.content.iter_chunked(
                    1024 * 16
                ):  # 16KB chunks
                    await f.write(chunk)

            return True, f"Downloaded {output_path}"

    except (aiohttp.ClientError, asyncio.TimeoutError) as e:
        # Clean up partially downloaded file if it exists
        if output_path.exists():
            output_path.unlink()
        return False, f"Error downloading {url}: {str(e)}"

    finally:
        if close_session and not session.closed:
            await session.close()


def ensure_directory(path: Path) -> Path:
    """
    Ensure a directory exists and return its path.

    Args:
        path: Directory path

    Returns:
        Path to the directory
    """
    path.mkdir(parents=True, exist_ok=True)
    return path


def get_unique_filename(directory: Path, filename: str) -> Path:
    """
    Get a unique filename in the specified directory.

    Args:
        directory: Directory path
        filename: Desired filename

    Returns:
        Path to a unique filename
    """
    directory.mkdir(parents=True, exist_ok=True)

    # Split filename and extension
    base, ext = os.path.splitext(filename)
    counter = 1
    new_path = directory / filename

    # Find a unique filename by appending a counter if needed
    while new_path.exists():
        new_path = directory / f"{base}_{counter}{ext}"
        counter += 1

    return new_path
