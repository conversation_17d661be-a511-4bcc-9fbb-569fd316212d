"""
Database package for the Coiny Classifier application.
"""

# Import common exports
from database.exports import MODEL_EXPORTS, SERVICE_EXPORTS, UTIL_EXPORTS

# Import models directly from their modules to avoid circular imports
from database.models.dataset import Dataset  # noqa: F401
from database.models.dataset_set import DatasetSet, SetType  # noqa: F401
from database.models.model_run import ModelRun  # noqa: F401
from database.models.version import ModelVersion  # noqa: F401

# Import services directly to avoid circular imports
# This is simpler and more reliable than lazy loading
from database.services import (  # noqa: F401
    DatasetError,
    DatasetService,
    ModelMetadataError,
    ModelMetadataService,
    ModelRunService,
    ModelRunServiceError,
    TrainingDataError,
    TrainingDataService,
)

# Import utilities
from database.utils import (  # noqa: F401
    download_file,
    ensure_directory,
    get_unique_filename,
)

__all__ = [
    # Models
    *MODEL_EXPORTS,
    # Services
    *SERVICE_EXPORTS,
    # Utils
    *UTIL_EXPORTS,
]
