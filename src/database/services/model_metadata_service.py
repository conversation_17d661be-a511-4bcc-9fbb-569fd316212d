"""
Model Metadata Service for fetching and managing model-related data.
"""

from typing import Dict, Optional
from uuid import UUID

from database.models.model import Model
from database.models.model_run import ModelRun
from database.services.base_service import BaseService
from database.supabase_client import fetch_data
from database.utils.model_factory import ModelValidationError
from utils.async_utils import maybe_await


class ModelMetadataError(Exception):
    """Base exception for model metadata related errors."""


class ModelMetadataService(BaseService[Model]):
    """Service for fetching and managing model metadata."""

    # Set the model class for this service
    model_class = Model

    # Define required fields for validation
    required_fields = ["uuid", "name", "architecture"]

    @classmethod
    async def get_model_metadata(
        cls,
        model_run_uuid: str | UUID,
        profile: Optional[str] = None,
    ) -> Dict:
        """
        Fetch all model-related metadata for a given model run.

        Args:
            model_run_uuid: UUID of the model run
            profile: Optional Supabase profile name

        Returns:
            Dictionary containing model, model version, and model run data

        Raises:
            ModelMetadataError: If any required data is missing or invalid
        """
        try:
            # Convert to string to ensure compatibility
            model_run_uuid_str = str(model_run_uuid)

            # Set up query with select parameter for simpler and more readable code
            query_params = {
                "select": "*, model_versions(*, models(*))",
                "filters": [{"column": "uuid", "value": model_run_uuid_str}],
            }

            # Fetch data with joins
            result = fetch_data("model_runs", query_params, profile=profile)

            # Safely await the result
            result = await maybe_await(result)

            if not result or len(result) == 0:
                raise ModelMetadataError(
                    f"Model run not found with UUID: {model_run_uuid}"
                )

            # Extract the model run data
            model_run = result[0]

            # Extract model version data - could be a single object or a list
            model_versions = model_run.get("model_versions")
            if not model_versions:
                raise ModelMetadataError(
                    f"Model version not found for model run {model_run_uuid}"
                )

            # Handle both list and direct object cases
            if isinstance(model_versions, list):
                model_version = model_versions[0] if model_versions else None
            else:
                model_version = model_versions

            if not model_version:
                raise ModelMetadataError(
                    f"Model version data is empty for model run {model_run_uuid}"
                )

            # Extract model data - could be a single object or a list
            models = model_version.get("models")
            if not models:
                raise ModelMetadataError(
                    f"Model not found for model version {model_version.get('uuid')}"
                )

            # Handle both list and direct object cases
            if isinstance(models, list):
                model = models[0] if models else None
            else:
                model = models

            if not model:
                raise ModelMetadataError(
                    f"Model data is empty for model version {model_version.get('uuid')}"
                )

            # Construct the complete metadata
            return {
                "model": model,
                "model_version": model_version,
                "model_run": model_run,
            }
        except Exception as e:
            if isinstance(e, ModelMetadataError):
                raise
            raise ModelMetadataError(f"Error fetching model metadata: {str(e)}") from e

    @classmethod
    async def get_model_run(
        cls, model_run_uuid: str | UUID, profile: Optional[str] = None
    ) -> Dict:
        """
        Fetch model run data by UUID.

        Args:
            model_run_uuid: UUID of the model run
            profile: Optional Supabase profile name

        Returns:
            Dictionary containing model run data

        Raises:
            ModelMetadataError: If model run is not found
        """
        model_runs = fetch_data(
            "model_runs",
            {"filters": [{"column": "uuid", "value": str(model_run_uuid)}]},
            profile=profile,
        )
        if not model_runs:
            raise ModelMetadataError(f"Model run not found with UUID: {model_run_uuid}")
        return model_runs[0]

    @classmethod
    async def get_model_version(
        cls, model_version_uuid: str | UUID, profile: Optional[str] = None
    ) -> Dict:
        """
        Fetch model version data by UUID.

        Args:
            model_version_uuid: UUID of the model version
            profile: Optional Supabase profile name

        Returns:
            Dictionary containing model version data

        Raises:
            ModelMetadataError: If model version is not found
        """
        model_versions = fetch_data(
            "model_versions",
            {"filters": [{"column": "uuid", "value": str(model_version_uuid)}]},
            profile=profile,
        )
        if not model_versions:
            raise ModelMetadataError(
                f"Model version not found with UUID: {model_version_uuid}"
            )
        return model_versions[0]

    @classmethod
    def validate_model_data(cls, model_data: Dict) -> None:
        """
        Validate model data.

        Args:
            model_data: Model data to validate

        Raises:
            ModelMetadataError: If model data is invalid
        """
        try:
            # Use the generic validation method
            cls.validate_data(data=model_data)
        except ModelValidationError as e:
            # Convert ModelValidationError to ModelMetadataError
            raise ModelMetadataError(str(e)) from e

    @classmethod
    def create_model_instance(cls, data: Dict, partial: bool = False) -> Model:
        """
        Create a model instance from dictionary data.

        Args:
            data: Dictionary containing the data
            partial: If True, allows partial data (missing non-required fields)

        Returns:
            An instance of the Model class

        Raises:
            ModelMetadataError: If validation fails
        """
        try:
            return cls.create_model(data=data, partial=partial)
        except ModelValidationError as e:
            raise ModelMetadataError(f"Failed to create model: {str(e)}") from e

    @classmethod
    def create_model_run_instance(cls, data: Dict, partial: bool = False) -> ModelRun:
        """
        Create a model run instance from dictionary data.

        Args:
            data: Dictionary containing the data
            partial: If True, allows partial data (missing non-required fields)

        Returns:
            An instance of the ModelRun class

        Raises:
            ModelMetadataError: If validation fails
        """
        try:
            return cls.create_model(data=data, model_class=ModelRun, partial=partial)
        except ModelValidationError as e:
            raise ModelMetadataError(f"Failed to create model run: {str(e)}") from e

    @classmethod
    async def get_model(
        cls, model_uuid: str | UUID, profile: Optional[str] = None
    ) -> Dict:
        """
        Fetch model data by UUID.

        Args:
            model_uuid: UUID of the model
            profile: Optional Supabase profile name

        Returns:
            Dictionary containing model data

        Raises:
            ModelMetadataError: If model is not found
        """
        models = fetch_data(
            "models",
            {"filters": [{"column": "uuid", "value": str(model_uuid)}]},
            profile=profile,
        )
        if not models:
            raise ModelMetadataError(f"Model not found with UUID: {model_uuid}")

        # Convert raw data to model instance
        try:
            return cls.create_model_instance(models[0])
        except ModelMetadataError as e:
            msg = f"Invalid model data for UUID {model_uuid}: {str(e)}"
            raise ModelMetadataError(msg) from e

    @classmethod
    async def update_model_run_status(
        cls,
        model_run_uuid: str | UUID,
        status: str,
        metrics: Optional[Dict] = None,
        profile: Optional[str] = None,
    ) -> bool:
        """
        Update the status of a model run.

        Args:
            model_run_uuid: UUID of the model run to update
            status: New status for the model run
            metrics: Optional metrics to include in the update
            profile: Optional Supabase profile name

        Returns:
            bool: True if the update was successful

        Note:
            This is a placeholder implementation. In a real application,
            this would update the model run record in the database.
        """
        # Suppress unused argument warnings by acknowledging them
        _ = model_run_uuid, status, metrics, profile

        # In a real implementation, this would update the model run in the database
        # For now, we'll just return True to indicate success
        # Implementation needed for database update
        return True
