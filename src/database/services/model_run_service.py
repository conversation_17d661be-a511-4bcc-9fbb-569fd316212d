"""
Model Run Service for updating model run data.

This service provides functionality for updating specific fields in model_run records,
particularly timing fields, metrics, and log paths. It follows the established patterns
from other database services in the codebase.
"""

import json
import logging
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from typing import Dict, Optional, Union
from uuid import UUID

from pydantic import BaseModel, Field, field_validator

from database.models.model_run import ModelRun
from database.services.base_service import BaseService
from database.supabase_client import fetch_data
from database.supabase_client import update_data as update_db_data
from database.utils.model_factory import ModelValidationError

# Configure logger
logger = logging.getLogger(__name__)


class ModelRunServiceError(Exception):
    """Base exception for model run service related errors."""


class ModelRunUpdateData(BaseModel):
    """Data model for model run updates."""

    end_time: Optional[datetime] = Field(None, description="End time of the run")
    start_time: Optional[datetime] = Field(None, description="Start time of the run")
    prepared_time: Optional[datetime] = Field(
        None, description="Time when the run data was prepared"
    )
    dataset_content_updated_at: Optional[datetime] = Field(
        None, description="Time when dataset content was last updated"
    )
    log_path: Optional[bool] = Field(
        None, description="Flag indicating if logs are available"
    )
    metrics: Optional[Dict] = Field(None, description="Run metrics and results")

    @field_validator("log_path")
    @classmethod
    def validate_log_path(cls, v):
        """Validate log path format."""
        if v is not None and not isinstance(v, bool):
            raise ValueError("log_path must be a boolean or None")
        return v

    @field_validator("metrics")
    @classmethod
    def validate_metrics(cls, v):
        """Validate metrics structure."""
        if v is not None and not isinstance(v, dict):
            raise ValueError("metrics must be a dictionary or None")
        return v


@dataclass
class ModelRunTimingUpdate:
    """Data class for model run timing updates."""

    model_run_uuid: Union[str, UUID]
    end_time: Optional[datetime] = None
    start_time: Optional[datetime] = None
    prepared_time: Optional[datetime] = None
    dataset_content_updated_at: Optional[datetime] = None
    profile: Optional[str] = None


@dataclass
class ModelRunMetricsUpdate:
    """Data class for model run metrics updates."""

    model_run_uuid: Union[str, UUID]
    metrics: Optional[Dict] = None
    metrics_file_path: Optional[Union[str, Path]] = None
    log_path: Optional[bool] = None
    profile: Optional[str] = None


@dataclass
class ModelRunCompleteUpdate:
    """Data class for comprehensive model run updates."""

    model_run_uuid: Union[str, UUID]
    end_time: Optional[datetime] = None
    start_time: Optional[datetime] = None
    prepared_time: Optional[datetime] = None
    dataset_content_updated_at: Optional[datetime] = None
    log_path: Optional[bool] = None
    metrics: Optional[Dict] = None
    metrics_file_path: Optional[Union[str, Path]] = None
    profile: Optional[str] = None


class ModelRunService(BaseService[ModelRun]):
    """Service for updating model run data."""

    # Set the model class for this service
    model_class = ModelRun

    # Define required fields for validation (minimal for updates)
    required_fields = ["uuid"]

    @classmethod
    async def update_model_run_times(cls, update_data: ModelRunTimingUpdate) -> Dict:
        """
        Update timing fields for a model run.

        Args:
            update_data: ModelRunTimingUpdate containing all update parameters

        Returns:
            Dictionary containing the updated model run data

        Raises:
            ModelRunServiceError: If the update fails or model run is not found
        """
        try:
            # Prepare update data, filtering out None values
            update_fields = {}
            if update_data.end_time is not None:
                update_fields["end_time"] = update_data.end_time.isoformat()
            if update_data.start_time is not None:
                update_fields["start_time"] = update_data.start_time.isoformat()
            if update_data.prepared_time is not None:
                update_fields["prepared_time"] = update_data.prepared_time.isoformat()
            if update_data.dataset_content_updated_at is not None:
                update_fields["dataset_content_updated_at"] = (
                    update_data.dataset_content_updated_at.isoformat()
                )

            if not update_fields:
                raise ModelRunServiceError(
                    "At least one timing field must be provided for update"
                )

            # Validate the model run exists
            await cls._validate_model_run_exists(
                update_data.model_run_uuid, update_data.profile
            )

            # Perform the update
            result = update_db_data(
                table="model_runs",
                data=update_fields,
                filters=[{"column": "uuid", "value": str(update_data.model_run_uuid)}],
                profile=update_data.profile,
            )

            if not result:
                raise ModelRunServiceError(
                    f"Failed to update model run {update_data.model_run_uuid}"
                )

            logger.info(
                "Updated timing fields for model run %s", update_data.model_run_uuid
            )
            return result[0] if result else {}

        except Exception as e:
            if isinstance(e, ModelRunServiceError):
                raise
            raise ModelRunServiceError(
                f"Error updating model run times: {str(e)}"
            ) from e

    @classmethod
    async def update_model_run_metrics(cls, update_data: ModelRunMetricsUpdate) -> Dict:
        """
        Update metrics and log path for a model run.

        Args:
            update_data: ModelRunMetricsUpdate containing all update parameters

        Returns:
            Dictionary containing the updated model run data

        Raises:
            ModelRunServiceError: If the update fails or model run is not found
        """
        try:
            # Load metrics from file if path is provided
            metrics = update_data.metrics
            if update_data.metrics_file_path is not None:
                metrics = cls.load_metrics_from_file(update_data.metrics_file_path)

            # Prepare update data, filtering out None values
            update_fields = {}
            if metrics is not None:
                update_fields["metrics"] = metrics
            if update_data.log_path is not None:
                update_fields["log_path"] = update_data.log_path

            if not update_fields:
                raise ModelRunServiceError(
                    "At least one field (metrics or log_path) must be provided for update"
                )

            # Validate the update data
            ModelRunUpdateData(**update_fields)

            # Validate the model run exists
            await cls._validate_model_run_exists(
                update_data.model_run_uuid, update_data.profile
            )

            # Perform the update
            result = update_db_data(
                table="model_runs",
                data=update_fields,
                filters=[{"column": "uuid", "value": str(update_data.model_run_uuid)}],
                profile=update_data.profile,
            )

            if not result:
                raise ModelRunServiceError(
                    f"Failed to update model run {update_data.model_run_uuid}"
                )

            logger.info(
                "Updated metrics/log_path for model run %s", update_data.model_run_uuid
            )
            return result[0] if result else {}

        except Exception as e:
            if isinstance(e, ModelRunServiceError):
                raise
            raise ModelRunServiceError(
                f"Error updating model run metrics: {str(e)}"
            ) from e

    @classmethod
    async def update_model_run_complete(
        cls, update_data: ModelRunCompleteUpdate
    ) -> Dict:
        """
        Comprehensive update for all supported model run fields.

        Args:
            update_data: ModelRunCompleteUpdate containing all update parameters

        Returns:
            Dictionary containing the updated model run data

        Raises:
            ModelRunServiceError: If the update fails or model run is not found
        """
        try:
            # Load metrics from file if path is provided
            metrics = update_data.metrics
            if update_data.metrics_file_path is not None:
                metrics = cls.load_metrics_from_file(update_data.metrics_file_path)

            # Prepare update data, filtering out None values
            update_fields = {}

            # Add timing fields
            if update_data.end_time is not None:
                update_fields["end_time"] = update_data.end_time.isoformat()
            if update_data.start_time is not None:
                update_fields["start_time"] = update_data.start_time.isoformat()
            if update_data.prepared_time is not None:
                update_fields["prepared_time"] = update_data.prepared_time.isoformat()
            if update_data.dataset_content_updated_at is not None:
                update_fields["dataset_content_updated_at"] = (
                    update_data.dataset_content_updated_at.isoformat()
                )

            # Add other fields
            if update_data.log_path is not None:
                update_fields["log_path"] = update_data.log_path
            if metrics is not None:
                update_fields["metrics"] = metrics

            if not update_fields:
                raise ModelRunServiceError(
                    "At least one field must be provided for update"
                )

            # Validate the update data
            ModelRunUpdateData(
                **{
                    k: v
                    for k, v in {
                        "end_time": update_data.end_time,
                        "start_time": update_data.start_time,
                        "prepared_time": update_data.prepared_time,
                        "dataset_content_updated_at": update_data.dataset_content_updated_at,
                        "log_path": update_data.log_path,
                        "metrics": metrics,
                    }.items()
                    if v is not None
                }
            )

            # Validate the model run exists
            await cls._validate_model_run_exists(
                update_data.model_run_uuid, update_data.profile
            )

            # Perform the update
            result = update_db_data(
                table="model_runs",
                data=update_fields,
                filters=[{"column": "uuid", "value": str(update_data.model_run_uuid)}],
                profile=update_data.profile,
            )

            if not result:
                raise ModelRunServiceError(
                    f"Failed to update model run {update_data.model_run_uuid}"
                )

            logger.info(
                "Updated model run %s with %d fields",
                update_data.model_run_uuid,
                len(update_fields),
            )
            return result[0] if result else {}

        except Exception as e:
            if isinstance(e, ModelRunServiceError):
                raise
            raise ModelRunServiceError(f"Error updating model run: {str(e)}") from e

    @classmethod
    def load_metrics_from_file(cls, metrics_file_path: Union[str, Path]) -> Dict:
        """
        Load metrics from a JSON file.

        Args:
            metrics_file_path: Path to the metrics_summary.json file

        Returns:
            Dictionary containing the metrics data

        Raises:
            ModelRunServiceError: If the file cannot be read or parsed
        """
        try:
            file_path = Path(metrics_file_path)

            if not file_path.exists():
                raise ModelRunServiceError(f"Metrics file not found: {file_path}")

            if not file_path.is_file():
                raise ModelRunServiceError(f"Metrics path is not a file: {file_path}")

            with open(file_path, "r", encoding="utf-8") as f:
                metrics_data = json.load(f)

            if not isinstance(metrics_data, dict):
                raise ModelRunServiceError("Metrics file must contain a JSON object")

            logger.info("Loaded metrics from file: %s", file_path)
            return metrics_data

        except json.JSONDecodeError as e:
            raise ModelRunServiceError(f"Invalid JSON in metrics file: {str(e)}") from e
        except Exception as e:
            if isinstance(e, ModelRunServiceError):
                raise
            raise ModelRunServiceError(f"Error loading metrics file: {str(e)}") from e

    @classmethod
    async def _validate_model_run_exists(
        cls, model_run_uuid: Union[str, UUID], profile: Optional[str] = None
    ) -> None:
        """
        Validate that a model run exists in the database.

        Args:
            model_run_uuid: UUID of the model run to validate
            profile: Optional Supabase profile name

        Raises:
            ModelRunServiceError: If the model run is not found
        """
        try:
            model_runs = fetch_data(
                "model_runs",
                {"filters": [{"column": "uuid", "value": str(model_run_uuid)}]},
                profile=profile,
            )

            if not model_runs:
                raise ModelRunServiceError(
                    f"Model run not found with UUID: {model_run_uuid}"
                )

        except Exception as e:
            if isinstance(e, ModelRunServiceError):
                raise
            raise ModelRunServiceError(
                f"Error validating model run existence: {str(e)}"
            ) from e

    @classmethod
    async def get_model_run(
        cls, model_run_uuid: Union[str, UUID], profile: Optional[str] = None
    ) -> Dict:
        """
        Fetch model run data by UUID.

        Args:
            model_run_uuid: UUID of the model run
            profile: Optional Supabase profile name

        Returns:
            Dictionary containing model run data

        Raises:
            ModelRunServiceError: If model run is not found
        """
        try:
            model_runs = fetch_data(
                "model_runs",
                {"filters": [{"column": "uuid", "value": str(model_run_uuid)}]},
                profile=profile,
            )

            if not model_runs:
                raise ModelRunServiceError(
                    f"Model run not found with UUID: {model_run_uuid}"
                )

            return model_runs[0]

        except Exception as e:
            if isinstance(e, ModelRunServiceError):
                raise
            raise ModelRunServiceError(f"Error fetching model run: {str(e)}") from e

    @classmethod
    def create_model_run_instance(cls, data: Dict, partial: bool = False) -> ModelRun:
        """
        Create a model run instance from dictionary data.

        Args:
            data: Dictionary containing the data
            partial: If True, allows partial data (missing non-required fields)

        Returns:
            An instance of the ModelRun class

        Raises:
            ModelRunServiceError: If validation fails
        """
        try:
            return cls.create_model(data=data, model_class=ModelRun, partial=partial)
        except ModelValidationError as e:
            raise ModelRunServiceError(f"Failed to create model run: {str(e)}") from e
