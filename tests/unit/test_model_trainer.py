"""
Tests for the ModelTrainer class in src.train.trainer module.
"""

import json
import tempfile
from pathlib import Path
from unittest import mock
from unittest.mock import MagicMock

import pytest
import torch
from torch import nn, optim
from torch.utils.data import DataLoader, TensorDataset

from src.train.callbacks import EarlyStoppingCallback
from src.train.trainer import DatabaseConfig, ModelTrainer, TrainerConfig


class SimpleModel(nn.Module):
    """A simple model for testing."""

    def __init__(self):
        super().__init__()
        self.linear = nn.Linear(10, 1)

    def forward(self, x):
        """Forward pass of the model."""
        return self.linear(x)


@pytest.fixture
def mock_data_loaders():
    """Create mock data loaders for training and testing."""
    # Create random data
    x_train = torch.randn(20, 10)
    y_train = torch.randint(0, 2, (20, 1)).float()
    x_test = torch.randn(10, 10)
    y_test = torch.randint(0, 2, (10, 1)).float()

    # Create datasets
    train_dataset = TensorDataset(x_train, y_train)
    test_dataset = TensorDataset(x_test, y_test)

    # Create data loaders
    train_loader = DataLoader(train_dataset, batch_size=5)
    test_loader = DataLoader(test_dataset, batch_size=5)

    return {"train": train_loader, "test": test_loader}


@pytest.fixture
def model_components():
    """Create model components for testing."""
    model = SimpleModel()
    loss_fn = nn.BCEWithLogitsLoss()
    optimizer = optim.SGD(model.parameters(), lr=0.01)

    return {"model": model, "loss_fn": loss_fn, "optimizer": optimizer}


@pytest.fixture
def training_config():
    """Create training configuration for testing."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield {
            "model_id": "test_model",
            "run_output_dir": temp_dir,
            "epochs": 2,
        }


# pylint: disable=redefined-outer-name
# This is needed because pytest fixtures are used as test function parameters
class TestModelTrainer:
    """Tests for the ModelTrainer class."""

    def test_initialization(self, model_components, mock_data_loaders, training_config):
        """Test that the ModelTrainer initializes correctly."""
        config = TrainerConfig(
            model_components=model_components,
            data_loaders=mock_data_loaders,
            training_config=training_config,
        )
        trainer = ModelTrainer(config)

        # Check that the trainer has the expected attributes
        assert trainer.components.model == model_components["model"]
        assert trainer.components.loss_fn == model_components["loss_fn"]
        assert trainer.components.optimizer == model_components["optimizer"]
        assert trainer.data_loaders == mock_data_loaders
        assert trainer.config.config == training_config

        # Check that metrics are initialized correctly
        assert hasattr(trainer.metrics, "train_losses")
        assert hasattr(trainer.metrics, "test_losses")
        assert hasattr(trainer.metrics, "train_accuracies")
        assert hasattr(trainer.metrics, "test_accuracies")
        assert hasattr(trainer.metrics, "timing")
        assert hasattr(trainer.metrics, "resources")
        assert hasattr(trainer.metrics, "custom_metrics")
        assert hasattr(trainer.metrics, "batch_metrics")

    def test_metrics_collection(
        self, model_components, mock_data_loaders, training_config
    ):
        """Test that metrics are collected correctly during training."""
        config = TrainerConfig(
            model_components=model_components,
            data_loaders=mock_data_loaders,
            training_config=training_config,
        )
        trainer = ModelTrainer(config)

        # Mock the _collect_resource_metrics method to avoid actual system calls
        with mock.patch.object(trainer, "_collect_resource_metrics"):
            # Train the model
            trainer.train()

            # Check that metrics were collected
            assert len(trainer.metrics.train_losses) == training_config["epochs"]
            assert len(trainer.metrics.test_losses) == training_config["epochs"]
            assert len(trainer.metrics.train_accuracies) == training_config["epochs"]
            assert len(trainer.metrics.test_accuracies) == training_config["epochs"]

            # Check timing metrics
            assert trainer.metrics.timing["start_time"] is not None
            assert trainer.metrics.timing["end_time"] is not None
            assert (
                len(trainer.metrics.timing["epoch_times"]) == training_config["epochs"]
            )
            assert (
                len(trainer.metrics.timing["train_times"]) == training_config["epochs"]
            )
            assert (
                len(trainer.metrics.timing["validation_times"])
                == training_config["epochs"]
            )

            # Check that total training time is calculated
            assert "total_training_time" in trainer.metrics.timing
            assert trainer.metrics.timing["total_training_time"] > 0

            # Check that batch metrics were collected
            assert len(trainer.metrics.batch_metrics) > 0

            # Check structure of batch metrics
            batch_metric = trainer.metrics.batch_metrics[0]
            assert "epoch" in batch_metric
            assert "batch" in batch_metric
            assert "loss" in batch_metric
            assert "accuracy" in batch_metric
            assert "time" in batch_metric

    def test_custom_metrics(self, model_components, mock_data_loaders, training_config):
        """Test that custom metrics can be added."""
        config = TrainerConfig(
            model_components=model_components,
            data_loaders=mock_data_loaders,
            training_config=training_config,
        )
        trainer = ModelTrainer(config)

        # Add custom metrics
        trainer.add_custom_metric("test_metric", 0.5)
        trainer.add_custom_metric("test_metric", 0.6)
        trainer.add_custom_metric("another_metric", 0.7)

        # Check that custom metrics were added
        assert "test_metric" in trainer.metrics.custom_metrics
        assert len(trainer.metrics.custom_metrics["test_metric"]) == 2
        assert trainer.metrics.custom_metrics["test_metric"] == [0.5, 0.6]

        assert "another_metric" in trainer.metrics.custom_metrics
        assert len(trainer.metrics.custom_metrics["another_metric"]) == 1
        assert trainer.metrics.custom_metrics["another_metric"] == [0.7]

    def test_get_metrics(self, model_components, mock_data_loaders, training_config):
        """Test that get_metrics returns a copy of the metrics."""
        config = TrainerConfig(
            model_components=model_components,
            data_loaders=mock_data_loaders,
            training_config=training_config,
        )
        trainer = ModelTrainer(config)

        # Add a custom metric
        trainer.add_custom_metric("test_metric", 0.5)

        # Get metrics
        metrics = trainer.get_metrics()

        # Check that metrics is a copy
        assert metrics is not trainer.metrics

        # Modify the returned metrics
        metrics["custom_metrics"]["test_metric"].append(0.6)

        # Check that the original metrics were not modified
        assert len(trainer.metrics.custom_metrics["test_metric"]) == 1
        assert trainer.metrics.custom_metrics["test_metric"] == [0.5]

    def test_metrics_saved_to_file(
        self, model_components, mock_data_loaders, training_config
    ):
        """Test that metrics are saved to files correctly."""
        config = TrainerConfig(
            model_components=model_components,
            data_loaders=mock_data_loaders,
            training_config=training_config,
        )
        trainer = ModelTrainer(config)

        # Mock the _collect_resource_metrics method to avoid actual system calls
        with mock.patch.object(trainer, "_collect_resource_metrics"):
            # Train the model
            trainer.train()

            # Check that metrics files were created
            run_output_dir = Path(training_config["run_output_dir"])
            metrics_path = run_output_dir / "metrics_summary.json"
            detailed_metrics_path = run_output_dir / "metrics_history.json"

            assert metrics_path.exists()
            assert detailed_metrics_path.exists()

            # Load and check metrics
            with open(metrics_path, "r", encoding="utf-8") as f:
                metrics = json.load(f)

            assert "final_train_loss" in metrics
            assert "final_test_loss" in metrics
            assert "final_train_accuracy" in metrics
            assert "final_test_accuracy" in metrics
            assert "timing" in metrics
            assert "resources" in metrics

            # Load and check detailed metrics
            with open(detailed_metrics_path, "r", encoding="utf-8") as f:
                detailed_metrics = json.load(f)

            assert "train_losses" in detailed_metrics
            assert "test_losses" in detailed_metrics
            assert "train_accuracies" in detailed_metrics
            assert "test_accuracies" in detailed_metrics
            assert "timing" in detailed_metrics
            assert "resources" in detailed_metrics
            assert "custom_metrics" in detailed_metrics

    def test_early_stopping_callback(
        self, model_components, mock_data_loaders, training_config
    ):
        """Test that the EarlyStoppingCallback stops training early."""
        # Use a higher epoch count to allow for early stopping
        training_config["epochs"] = 10
        patience = 2

        # Initialize the callback
        early_stopping_callback = EarlyStoppingCallback(
            monitor="validation_loss", patience=patience, verbose=False
        )

        # Initialize the trainer with the callback
        config = TrainerConfig(
            model_components=model_components,
            data_loaders=mock_data_loaders,
            training_config=training_config,
            callbacks=[early_stopping_callback],
        )
        trainer = ModelTrainer(config)

        # Mock the validation epoch to simulate no improvement
        # The first call should establish a baseline, subsequent calls should be worse.
        validation_losses = [0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
        side_effects = [
            {"validation_loss": loss, "validation_accuracy": 0.5}
            for loss in validation_losses
        ]

        with mock.patch.object(
            trainer, "_validate_epoch", side_effect=side_effects
        ) as mock_validate:
            # Mock the training epoch to return some dummy metrics
            with mock.patch.object(
                trainer,
                "_train_epoch",
                return_value={"train_loss": 0.5, "train_accuracy": 0.5},
            ):
                # Mock resource collection
                with mock.patch.object(trainer, "_collect_resource_metrics"):
                    trainer.train()

                    # The first epoch sets the baseline.
                    # The next `patience` epochs will not improve.
                    # Training should stop after `1 + patience` epochs.
                    expected_epochs = 1 + patience
                    assert len(trainer.metrics.train_losses) == expected_epochs
                    assert mock_validate.call_count == expected_epochs
                    assert trainer.stop_training is True

    def test_database_config_integration(
        self, model_components, mock_data_loaders, training_config
    ):
        """Test that DatabaseConfig is properly integrated."""

        # Test with database config
        database_config = DatabaseConfig(
            model_run_uuid="test-uuid-123", profile="development"
        )

        # Mock the ModelRunCallback import to avoid actual database dependencies
        with mock.patch("src.train.trainer.ModelRunCallback") as mock_callback_class:
            mock_callback_instance = MagicMock()
            mock_callback_class.return_value = mock_callback_instance

            config = TrainerConfig(
                model_components=model_components,
                data_loaders=mock_data_loaders,
                training_config=training_config,
                database_config=database_config,
            )
            trainer = ModelTrainer(config)

            # Verify that ModelRunCallback was created with correct parameters
            mock_callback_class.assert_called_once_with(
                model_run_uuid="test-uuid-123",
                profile="development",
                verbose=True,
            )

            # Verify that the callback was added to the handler
            assert mock_callback_instance in trainer.callback_handler.callbacks

    def test_database_config_none(
        self, model_components, mock_data_loaders, training_config
    ):
        """Test that trainer works without database config."""
        config = TrainerConfig(
            model_components=model_components,
            data_loaders=mock_data_loaders,
            training_config=training_config,
            database_config=None,
        )
        trainer = ModelTrainer(config)

        # Should have no callbacks
        assert len(trainer.callback_handler.callbacks) == 0
