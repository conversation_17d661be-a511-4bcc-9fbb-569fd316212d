"""
Centralized data fixtures for tests.

This module provides common data structures used across different test modules,
ensuring consistency and reducing duplication.
"""

import pytest

# Common test values
# Dataset related constants
TEST_UUID = "b9dd3efa-cbe2-4a97-b895-ebb7a8e38fb5"  # Matches example in Dataset model
TEST_TIMESTAMP = "2025-02-27T18:28:18.77991+00:00"  # Matches example in Dataset model
TEST_DATASET_UUID = "test-dataset-123"
TEST_IMAGE_UUID_1 = "img-1"
TEST_IMAGE_UUID_2 = "img-2"
TEST_COIN_SIDE_UUID_1 = "side-1"
TEST_COIN_SIDE_UUID_2 = "side-2"
TEST_USER_ID = "c368cec9-9875-435c-8e1c-cca131b4faad"
TEST_COVER = (
    "e3f77acefb06d8c1899f4c0e1fdfa1d943119ca8.png"  # Matches example in Dataset model
)
TEST_IMAGES_COUNT = 534003  # Matches example in Dataset model

# Model related constants
TEST_MODEL_UUID = (
    "123e4567-e89b-12d3-a456-************"  # Matches example in Model model
)
TEST_MODEL_NAME = "Euro Coin Classifier"
TEST_MODEL_DESCRIPTION = "Model for classifying Euro coins"
TEST_MODEL_ARCHITECTURE = "ResNet50"
TEST_MODEL_USER_ID = "98765432-e89b-12d3-a456-************"
TEST_MODEL_TIMESTAMP = "2024-01-20T12:00:00Z"

# ModelVersion related constants
TEST_MODEL_VERSION_UUID = (
    "bbf27ea7-9998-490d-b7da-6be47522fe4e"  # Matches example in ModelVersion model
)
TEST_MODEL_VERSION_NUMBER = 1
TEST_MODEL_VERSION_NAME = "Initial Model Version"
TEST_MODEL_VERSION_NOTE = "This is the first version of the model."
TEST_MODEL_VERSION_TIMESTAMP = "2023-10-01T12:00:00Z"

# ModelRun related constants
TEST_MODEL_RUN_UUID = (
    "031dafbc-7a8e-466e-bf7e-867ebd3325c1"  # Matches example in ModelRun model
)
TEST_MODEL_RUN_TIMESTAMP = "2023-10-01T12:00:00Z"
TEST_MODEL_RUN_SCHEDULE_TIME = "2023-10-02T12:00:00Z"
TEST_MODEL_RUN_PREPARED_TIME = "2023-10-02T12:15:00Z"
TEST_MODEL_RUN_START_TIME = "2023-10-01T12:30:00Z"
TEST_MODEL_RUN_ENVIRONMENT = "Python 3.8, TensorFlow 2.4"
TEST_MODEL_RUN_NOTE = "Initial run with baseline parameters."
TEST_MODEL_RUN_DATASET_CONTENT_UPDATED = "2023-09-30T12:00:00Z"


def get_sample_dataset_data(custom_values=None):
    """
    Get sample dataset data for testing.

    This function returns a consistent dataset dictionary that can be used
    across different test modules. The data structure matches the Dataset model
    defined in src/database/models/dataset.py.

    Args:
        custom_values (dict, optional): Dictionary of custom values to override
            defaults. Use this to create variations of the dataset for specific
            test cases.

    Returns:
        dict: A complete dataset dictionary with all required fields.
    """
    # Default dataset that matches the example in Dataset model Config
    data = {
        "uuid": TEST_UUID,
        "name": "Landscape",
        "description": (
            "The coin side includes a complicated illustration "
            "of natural scene with wildlife in any landscape."
        ),
        "user_id": TEST_USER_ID,
        "cover": TEST_COVER,
        "images_count": TEST_IMAGES_COUNT,
        "created_at": TEST_TIMESTAMP,
        "content_updated_at": None,
    }

    # Override with any custom values
    if custom_values:
        data.update(custom_values)

    return data


def get_sample_dataset_list():
    """
    Get sample list of datasets for testing.

    This function returns a list of dataset dictionaries that can be used
    for testing endpoints that return multiple datasets.

    Returns:
        list: A list of dataset dictionaries.
    """
    return [
        get_sample_dataset_data(
            custom_values={
                "name": "Test Dataset 1",
                "description": "First test dataset",
                "images_count": 100,
            }
        ),
        get_sample_dataset_data(
            custom_values={
                "uuid": "5d96f65c-35e9-5314-c8cd-5a7f7b999805",
                "name": "Test Dataset 2",
                "description": "Second test dataset",
                "cover": "another-cover.png",
                "images_count": 50,
            }
        ),
    ]


@pytest.fixture
def sample_dataset_data():
    """
    Sample dataset data for testing.

    This fixture provides a consistent dataset dictionary that can be used
    across different test modules.

    Returns:
        dict: A complete dataset dictionary with all required fields.
    """
    return get_sample_dataset_data()


@pytest.fixture
def sample_dataset_list():
    """
    Sample list of datasets for testing.

    This fixture provides a list of dataset dictionaries that can be used
    for testing endpoints that return multiple datasets.

    Returns:
        list: A list of dataset dictionaries.
    """
    return get_sample_dataset_list()


def get_sample_dataset_set(custom_values=None):
    """
    Get a single sample dataset set for testing.

    This function returns a consistent dataset set dictionary that can be used
    across different test modules. The data structure matches the DatasetSet model
    defined in src/database/models/dataset_set.py.

    Args:
        custom_values (dict, optional): Dictionary of custom values to override
            defaults. Use this to create variations of the dataset set for specific
            test cases.

    Returns:
        dict: A complete dataset set dictionary with all required fields.
    """
    # Default dataset set that matches the example in DatasetSet model Config
    data = {
        "uuid": "f1561add-6a2c-48ca-9911-69b10bf3f983",
        "dataset_uuid": TEST_DATASET_UUID,
        "image_uuid": TEST_IMAGE_UUID_1,
        "coin_side_uuid": TEST_COIN_SIDE_UUID_1,
        "set_type": 1,  # TRAIN
        "created_at": TEST_TIMESTAMP,
        "images_reviews": {"image_url": "https://example.com/image_4428.jpg"},
    }

    # Override with any custom values
    if custom_values:
        data.update(custom_values)

    return data


def get_sample_dataset_sets(count=3, custom_values_list=None):
    """
    Get sample dataset sets for testing.

    This function returns a list of dataset set dictionaries that can be used
    across different test modules. The data structure matches the DatasetSet model
    defined in src/database/models/dataset_set.py.

    Args:
        count (int, optional): Number of dataset sets to generate. Defaults to 3.
        custom_values_list (list, optional): List of dictionaries with custom values
            to override defaults for each dataset set. If provided, must have the
            same length as count.

    Returns:
        list: A list of dataset set dictionaries.
    """
    if custom_values_list and len(custom_values_list) != count:
        raise ValueError("custom_values_list must have the same length as count")

    # Default dataset sets with different types (TRAIN, VALIDATION, TEST)
    result = [
        get_sample_dataset_set(
            custom_values={
                "uuid": f"set-{i+1}",
                "set_type": min(i + 1, 3),  # 1=TRAIN, 2=VALIDATION, 3=TEST
                "image_uuid": TEST_IMAGE_UUID_1 if i % 2 == 0 else TEST_IMAGE_UUID_2,
                "coin_side_uuid": (
                    TEST_COIN_SIDE_UUID_1 if i % 2 == 0 else TEST_COIN_SIDE_UUID_2
                ),
                "images_reviews": {"image_url": f"http://example.com/image{i+1}.jpg"},
            }
        )
        for i in range(count)
    ]

    # Apply custom values if provided
    if custom_values_list:
        for i, custom_values in enumerate(custom_values_list):
            result[i].update(custom_values)

    return result


@pytest.fixture
def sample_dataset_sets():
    """
    Sample dataset sets for testing.

    This fixture provides a list of dataset set dictionaries that can be used
    for testing dataset set related functionality.
    """
    return get_sample_dataset_sets()


def get_sample_model_data(custom_values=None):
    """
    Get sample model data for testing.

    This function returns a consistent model dictionary that can be used
    across different test modules. The data structure matches the Model model
    defined in src/database/models/model.py.

    Args:
        custom_values (dict, optional): Dictionary of custom values to override
            defaults. Use this to create variations of the model for specific
            test cases.

    Returns:
        dict: A complete model dictionary with all required fields.
    """
    # Default model that matches the example in Model model Config
    data = {
        "uuid": TEST_MODEL_UUID,
        "name": TEST_MODEL_NAME,
        "description": TEST_MODEL_DESCRIPTION,
        "architecture": TEST_MODEL_ARCHITECTURE,
        "user_id": TEST_MODEL_USER_ID,
        "created_at": TEST_MODEL_TIMESTAMP,
    }

    # Override with any custom values
    if custom_values:
        data.update(custom_values)

    return data


@pytest.fixture
def sample_model_data():
    """
    Sample model data for testing.

    This fixture provides a dictionary representing a model that can be used
    for testing model-related functionality.

    Returns:
        dict: A complete model dictionary with all required fields.
    """
    return get_sample_model_data()


def get_sample_model_version_parameters_data(custom_values=None):
    """
    Get sample model version parameters data for testing.

    This function returns a consistent model version parameters dictionary that can be
    across different test modules. The data structure matches the ModelVersionParameters
    model defined in src/database/models/version.py.

    Args:
        custom_values (dict, optional): Dictionary of custom values to override
            defaults. Use this to create variations of the parameters for specific
            test cases.

    Returns:
        dict: A complete model version parameters dictionary with all required fields.
    """
    # Default parameters that match the example in ModelVersionParameters model Config
    data = {
        "activation": "relu",
        "weight_init": "kaiming_normal",
        "residual_blocks": 16,
        "dense_blocks": 4,
        "transition_layers": 3,
        "layers_number": 50,
        "encoder_decoder": "transformer",
        "embedding_dimension": 768,
        "attention_heads": 8,
        "auxiliary_classifier": "attention",
        "kernel_size": [3, 3],
        "stride": [2, 2],
        "convolutional_layers": [[3, 3, 1], [5, 5, 2]],
        "depth_separable_convolutions": [[3, 1], [5, 2]],
        "inception_modules": [[1, 64], [3, 96]],
        "batch_norm": 1,
        "regularisation": 0.001,
        "pooling": [0, 2, 2],
        "upsampling": 2,
        "grid_size": 16,
        "resolution_multiplier": 1.5,
        "width_multiplier": 1.0,
        "compound_scaling": [1.2, 1.1, 1.15],
        "growth_rate": 32,
        "input_size": [224, 224, 3],
        "patch_size": [16, 16],
        "fully_connected_layers": [1024, 512],
        "anchor_boxes": 9,
        "bounding_boxes": 4,
        "optimiser": "adam",
    }

    # Override with any custom values
    if custom_values:
        data.update(custom_values)

    return data


@pytest.fixture
def sample_model_version_parameters_data():
    """
    Sample model version parameters data for testing.

    This fixture provides a dictionary representing model version parameters
    that can be used for testing model version parameter-related functionality.

    Returns:
        dict: A complete model version parameters dictionary with all required fields.
    """
    return get_sample_model_version_parameters_data()


def get_sample_model_version_data(custom_values=None):
    """
    Get sample model version data for testing.

    This function returns a consistent model version dictionary that
    can be used across different test modules. The data structure matches
    the ModelVersion model defined in src/database/models/version.py.

    Args:
        custom_values (dict, optional): Dictionary of custom values to override
            defaults. Use this to create variations of the model version for specific
            test cases.

    Returns:
        dict: A complete model version dictionary with all required fields.
    """
    # Default model version that matches the example in ModelVersion model Config
    data = {
        "uuid": TEST_MODEL_VERSION_UUID,
        "model_uuid": TEST_MODEL_UUID,
        "version_number": TEST_MODEL_VERSION_NUMBER,
        "parameters": {"learning_rate": 0.01, "batch_size": 32, "epochs": 10},
        "created_at": TEST_MODEL_VERSION_TIMESTAMP,
        "user_id": TEST_MODEL_USER_ID,
        "name": TEST_MODEL_VERSION_NAME,
        "note": TEST_MODEL_VERSION_NOTE,
    }

    # Override with any custom values
    if custom_values:
        data.update(custom_values)

    return data


@pytest.fixture
def sample_model_version_data():
    """
    Sample model version data for testing.

    This fixture provides a dictionary representing a model version that can be used
    for testing model version-related functionality.

    Returns:
        dict: A complete model version dictionary with all required fields.
    """
    return get_sample_model_version_data()


def get_sample_model_run_augmentations():
    """
    Get sample model run augmentations for testing.

    This function returns a list of augmentation dictionaries that can be used
    in model run test data.

    Returns:
        list: A list of augmentation dictionaries.
    """
    return [
        {"horizontal_flip": True},
        {"rotation_range": 15.0},
        {"normalization_mean": [0.485, 0.456, 0.406]},
    ]


def get_sample_model_run_metrics():
    """
    Get sample model run metrics for testing.

    This function returns a dictionary of metrics that can be used
    in model run test data.

    Returns:
        dict: A dictionary of model run metrics.
    """
    return {"accuracy": 0.95, "loss": 0.05}


def get_sample_model_run_parameters():
    """
    Get sample model run parameters for testing.

    This function returns a dictionary of parameters that can be used
    in model run test data.

    Returns:
        dict: A dictionary of model run parameters.
    """
    return {"learning_rate": 0.001, "batch_size": 64}


def get_sample_model_run_data(custom_values=None):
    """
    Get sample model run data for testing.

    This function returns a consistent model run dictionary that
    can be used across different test modules. The data structure matches
    the ModelRun model defined in src/database/models/model_run.py.

    Args:
        custom_values (dict, optional): Dictionary of custom values to override
            defaults. Use this to create variations of the model run for specific
            test cases.

    Returns:
        dict: A complete model run dictionary with all required fields.
    """
    # Default model run that matches the example in ModelRun model Config
    data = {
        "uuid": TEST_MODEL_RUN_UUID,
        "created_at": TEST_MODEL_RUN_TIMESTAMP,
        "is_training": True,
        "is_testing": False,
        "model_version_uuid": TEST_MODEL_VERSION_UUID,
        "dataset_uuid": TEST_DATASET_UUID,
        "schedule_time": TEST_MODEL_RUN_SCHEDULE_TIME,
        "prepared_time": TEST_MODEL_RUN_PREPARED_TIME,
        "start_time": TEST_MODEL_RUN_START_TIME,
        "end_time": None,
        "metrics": get_sample_model_run_metrics(),
        "log_path": "/logs/model_run_031dafbc.log",
        "model_artifact_path": "/artifacts/model_run_031dafbc.zip",
        "parameters": get_sample_model_run_parameters(),
        "augmentations": get_sample_model_run_augmentations(),
        "environment_info": TEST_MODEL_RUN_ENVIRONMENT,
        "experiment_uuid": "some-experiment-uuid",
        "note": TEST_MODEL_RUN_NOTE,
        "dataset_content_updated_at": TEST_MODEL_RUN_DATASET_CONTENT_UPDATED,
    }

    # Override with any custom values
    if custom_values:
        data.update(custom_values)

    return data


@pytest.fixture
def sample_model_run_data():
    """
    Sample model run data for testing.

    This fixture provides a dictionary representing a model run that can be used
    for testing model run-related functionality.

    Returns:
        dict: A complete model run dictionary with all required fields.
    """
    return get_sample_model_run_data()
