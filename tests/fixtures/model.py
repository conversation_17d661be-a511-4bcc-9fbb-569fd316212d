"""
Model-specific fixtures for testing.

This module provides fixtures specifically for testing ML models.
"""

import pytest
import torch


@pytest.fixture
def sample_image_batch():
    """
    Create a sample batch of images for testing.

    Returns:
        torch.Tensor: A tensor of shape (32, 1, 91, 91) representing a batch
                     of 32 grayscale images of size 91x91 pixels.
    """
    # Create a batch of 32 random images with shape (1, 91, 91)
    batch_size = 32
    image_size = 91
    torch.manual_seed(42)  # For reproducibility
    return torch.randn(batch_size, 1, image_size, image_size)


@pytest.fixture
def sample_labels():
    """
    Create sample binary labels for testing.

    Returns:
        torch.Tensor: A tensor of shape (32, 1) containing binary labels
                     (0.0 or 1.0).
    """
    # Create 32 binary labels
    batch_size = 32
    torch.manual_seed(42)  # For reproducibility
    return torch.randint(0, 2, (batch_size, 1)).float()


@pytest.fixture(name="mock_model_weights")
def mock_model_weights():
    """
    Create mock model weights for testing a dynamic CNN.

    Returns:
        dict: A dictionary containing mock weights for a dynamic CNN model.
    """
    torch.manual_seed(42)  # For reproducibility

    # These dimensions correspond to the default dynamic_cnn_params
    # in tests/models/test_dynamic_cnn.py
    # Input image: 91x91, 1 channel -> after convs and pool -> 6x45x45 feature map
    num_features_before_fc = 6 * 45 * 45
    return {
        # Conv1 + BatchNorm1
        "0.weight": torch.randn(3, 1, 3, 3),
        "0.bias": torch.randn(3),
        "1.weight": torch.randn(3),
        "1.bias": torch.randn(3),
        "1.running_mean": torch.randn(3),
        "1.running_var": torch.randn(3),
        "1.num_batches_tracked": torch.tensor(0),
        # Conv2 + BatchNorm2
        "3.weight": torch.randn(6, 3, 3, 3),
        "3.bias": torch.randn(6),
        "4.weight": torch.randn(6),
        "4.bias": torch.randn(6),
        "4.running_mean": torch.randn(6),
        "4.running_var": torch.randn(6),
        "4.num_batches_tracked": torch.tensor(0),
        # FC1 (index 8 in the nn.Sequential)
        "8.weight": torch.randn(50, num_features_before_fc),
        "8.bias": torch.randn(50),
        # FC2 (index 11 in the nn.Sequential)
        "11.weight": torch.randn(1, 50),
        "11.bias": torch.randn(1),
    }
