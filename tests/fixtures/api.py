"""
API-related test fixtures and configuration.

This module provides fixtures for FastAPI application testing.
"""

from unittest.mock import patch

import pytest
from fastapi import FastAPI
from fastapi.testclient import TestClient

from api.main import app


@pytest.fixture
def api_test_app():
    """Create a test FastAPI app for API tests."""
    test_app = FastAPI()

    @test_app.get("/test")
    def test_endpoint():
        return {"status": "ok"}

    return test_app


@pytest.fixture
def api_test_client():
    """Create a TestClient for the main app."""
    return TestClient(app)


@pytest.fixture(name="test_client")
def fixture_test_client() -> TestClient:
    """
    Create a test client for the FastAPI application with mocked dependencies.

    Returns:
        TestClient: A test client for the FastAPI application.
    """
    # Create a test client with the app
    client = TestClient(app)
    return client


@pytest.fixture
def mock_supabase_profile_middleware():
    """Mock the SupabaseProfileMiddleware."""
    with patch("api.middleware.supabase_profile.SupabaseProfileMiddleware") as mock:
        yield mock
