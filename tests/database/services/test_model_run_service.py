"""
Tests for the ModelRunService.
"""

import json
import tempfile
from datetime import datetime
from pathlib import Path
from unittest.mock import patch
from uuid import uuid4

import pytest
from pydantic import ValidationError

from database.services.model_run_service import (
    ModelRunMetricsUpdate,
    ModelRunService,
    ModelRunServiceError,
    ModelRunTimingUpdate,
    ModelRunUpdateData,
)


class TestModelRunUpdateData:
    """Test the ModelRunUpdateData validation model."""

    def test_valid_data(self):
        """Test creating ModelRunUpdateData with valid data."""
        now = datetime.now()
        data = ModelRunUpdateData(
            end_time=now,
            start_time=now,
            dataset_content_updated_at=now,
            log_path=True,
            metrics={"accuracy": 0.95, "loss": 0.05},
        )

        assert data.end_time == now
        assert data.start_time == now
        assert data.dataset_content_updated_at == now
        assert data.log_path is True
        assert data.metrics == {"accuracy": 0.95, "loss": 0.05}

    def test_none_values(self):
        """Test that None values are accepted."""
        data = ModelRunUpdateData()

        assert data.end_time is None
        assert data.start_time is None
        assert data.dataset_content_updated_at is None
        assert data.log_path is None
        assert data.metrics is None

    def test_invalid_log_path(self):
        """Test validation of log_path field."""
        with pytest.raises(ValidationError, match="Input should be a valid boolean"):
            ModelRunUpdateData(log_path="invalid")

    def test_invalid_metrics(self):
        """Test validation of metrics field."""
        with pytest.raises(ValidationError, match="Input should be a valid dictionary"):
            ModelRunUpdateData(metrics="invalid")


class TestModelRunService:
    """Test the ModelRunService class."""

    @pytest.fixture(name="sample_model_run_uuid")
    def fixture_sample_model_run_uuid(self):
        """Fixture for a sample model run UUID."""
        return str(uuid4())

    @pytest.fixture(name="sample_metrics")
    def fixture_sample_metrics(self):
        """Fixture for sample metrics data."""
        return {
            "final_train_loss": 0.1234,
            "final_train_accuracy": 0.8765,
            "final_test_loss": 0.2345,
            "final_test_accuracy": 0.7654,
            "timing": {"train_times": [10.5, 11.2, 9.8]},
            "resources": {"memory_usage": [1024, 1056, 1089]},
        }

    @pytest.fixture(name="temp_metrics_file")
    def fixture_temp_metrics_file(self, sample_metrics):
        """Fixture for a temporary metrics file."""
        with tempfile.NamedTemporaryFile(mode="w", suffix=".json", delete=False) as f:
            json.dump(sample_metrics, f)
            temp_path = Path(f.name)

        yield temp_path

        # Cleanup
        if temp_path.exists():
            temp_path.unlink()

    @pytest.fixture(name="metrics_test_data")
    def fixture_metrics_test_data(
        self, sample_model_run_uuid, temp_metrics_file, sample_metrics
    ):
        """Composite fixture for metrics testing."""
        return {
            "model_run_uuid": sample_model_run_uuid,
            "temp_metrics_file": temp_metrics_file,
            "sample_metrics": sample_metrics,
        }

    @patch("database.services.model_run_service.fetch_data")
    @patch("database.services.model_run_service.update_db_data")
    @pytest.mark.asyncio
    async def test_update_model_run_times_success(
        self, mock_update_data, mock_fetch_data, sample_model_run_uuid
    ):
        """Test successful update of model run timing fields."""
        # Setup mocks
        mock_fetch_data.return_value = [{"uuid": sample_model_run_uuid}]
        mock_update_data.return_value = [
            {"uuid": sample_model_run_uuid, "start_time": "2023-01-01T12:00:00"}
        ]

        now = datetime.now()

        # Call the method
        update_data = ModelRunTimingUpdate(
            model_run_uuid=sample_model_run_uuid,
            start_time=now,
            end_time=now,
            profile="development",
        )
        result = await ModelRunService.update_model_run_times(update_data)

        # Verify the result
        assert result["uuid"] == sample_model_run_uuid

        # Verify fetch_data was called to validate existence
        mock_fetch_data.assert_called_once_with(
            "model_runs",
            {"filters": [{"column": "uuid", "value": sample_model_run_uuid}]},
            profile="development",
        )

        # Verify update_data was called with correct parameters
        mock_update_data.assert_called_once()
        call_args = mock_update_data.call_args
        assert call_args[1]["table"] == "model_runs"
        assert call_args[1]["filters"] == [
            {"column": "uuid", "value": sample_model_run_uuid}
        ]
        assert call_args[1]["profile"] == "development"

        # Check that datetime fields were converted to ISO format
        update_data = call_args[1]["data"]
        assert "start_time" in update_data
        assert "end_time" in update_data

    @patch("database.services.model_run_service.fetch_data")
    @pytest.mark.asyncio
    async def test_update_model_run_times_not_found(
        self, mock_fetch_data, sample_model_run_uuid
    ):
        """Test update when model run is not found."""
        # Setup mock to return empty list (not found)
        mock_fetch_data.return_value = []

        now = datetime.now()

        # Call the method and expect an error
        update_data = ModelRunTimingUpdate(
            model_run_uuid=sample_model_run_uuid,
            start_time=now,
        )
        with pytest.raises(ModelRunServiceError, match="Model run not found"):
            await ModelRunService.update_model_run_times(update_data)

    @pytest.mark.asyncio
    async def test_update_model_run_times_no_fields(self, sample_model_run_uuid):
        """Test update when no timing fields are provided."""
        update_data = ModelRunTimingUpdate(model_run_uuid=sample_model_run_uuid)
        with pytest.raises(
            ModelRunServiceError, match="At least one timing field must be provided"
        ):
            await ModelRunService.update_model_run_times(update_data)

    @patch("database.services.model_run_service.fetch_data")
    @patch("database.services.model_run_service.update_db_data")
    @pytest.mark.asyncio
    async def test_update_model_run_metrics_success(
        self, mock_update_data, mock_fetch_data, sample_model_run_uuid, sample_metrics
    ):
        """Test successful update of model run metrics."""
        # Setup mocks
        mock_fetch_data.return_value = [{"uuid": sample_model_run_uuid}]
        mock_update_data.return_value = [
            {"uuid": sample_model_run_uuid, "metrics": sample_metrics}
        ]

        # Call the method
        update_data = ModelRunMetricsUpdate(
            model_run_uuid=sample_model_run_uuid,
            metrics=sample_metrics,
            log_path=True,
            profile="development",
        )
        result = await ModelRunService.update_model_run_metrics(update_data)

        # Verify the result
        assert result["uuid"] == sample_model_run_uuid

        # Verify update_data was called with correct parameters
        mock_update_data.assert_called_once()
        call_args = mock_update_data.call_args
        update_data = call_args[1]["data"]
        assert update_data["metrics"] == sample_metrics
        assert update_data["log_path"] is True

    @patch("database.services.model_run_service.fetch_data")
    @patch("database.services.model_run_service.update_db_data")
    @pytest.mark.asyncio
    async def test_update_model_run_metrics_from_file(
        self, mock_update_data, mock_fetch_data, metrics_test_data
    ):
        """Test successful update of model run metrics from file."""
        # Setup mocks
        mock_fetch_data.return_value = [{"uuid": metrics_test_data["model_run_uuid"]}]
        mock_update_data.return_value = [
            {
                "uuid": metrics_test_data["model_run_uuid"],
                "metrics": metrics_test_data["sample_metrics"],
            }
        ]

        # Call the method
        update_data = ModelRunMetricsUpdate(
            model_run_uuid=metrics_test_data["model_run_uuid"],
            metrics_file_path=metrics_test_data["temp_metrics_file"],
            profile="development",
        )
        result = await ModelRunService.update_model_run_metrics(update_data)

        # Verify the result
        assert result["uuid"] == metrics_test_data["model_run_uuid"]

        # Verify update_data was called with metrics from file
        mock_update_data.assert_called_once()
        call_args = mock_update_data.call_args
        update_data = call_args[1]["data"]
        assert update_data["metrics"] == metrics_test_data["sample_metrics"]

    def test_load_metrics_from_file_success(self, temp_metrics_file, sample_metrics):
        """Test successful loading of metrics from file."""
        result = ModelRunService.load_metrics_from_file(temp_metrics_file)
        assert result == sample_metrics

    def test_load_metrics_from_file_not_found(self):
        """Test loading metrics from non-existent file."""
        with pytest.raises(ModelRunServiceError, match="Metrics file not found"):
            ModelRunService.load_metrics_from_file("/non/existent/file.json")

    def test_load_metrics_from_file_invalid_json(self):
        """Test loading metrics from file with invalid JSON."""
        with tempfile.NamedTemporaryFile(mode="w", suffix=".json", delete=False) as f:
            f.write("invalid json content")
            temp_path = Path(f.name)

        try:
            with pytest.raises(
                ModelRunServiceError, match="Invalid JSON in metrics file"
            ):
                ModelRunService.load_metrics_from_file(temp_path)
        finally:
            temp_path.unlink()

    @patch("database.services.model_run_service.fetch_data")
    @pytest.mark.asyncio
    async def test_get_model_run_success(self, mock_fetch_data, sample_model_run_uuid):
        """Test successful retrieval of model run."""
        expected_data = {"uuid": sample_model_run_uuid, "status": "completed"}
        mock_fetch_data.return_value = [expected_data]

        result = await ModelRunService.get_model_run(sample_model_run_uuid)

        assert result == expected_data
        mock_fetch_data.assert_called_once_with(
            "model_runs",
            {"filters": [{"column": "uuid", "value": sample_model_run_uuid}]},
            profile=None,
        )

    @patch("database.services.model_run_service.fetch_data")
    @pytest.mark.asyncio
    async def test_get_model_run_not_found(
        self, mock_fetch_data, sample_model_run_uuid
    ):
        """Test retrieval of non-existent model run."""
        mock_fetch_data.return_value = []

        with pytest.raises(ModelRunServiceError, match="Model run not found"):
            await ModelRunService.get_model_run(sample_model_run_uuid)
