"""
Global PyTest configuration file.
This file contains global fixtures and configuration that apply to all tests.
Component-specific fixtures are in their respective conftest.py files.
"""

import sys
from importlib import reload
from pathlib import Path
from unittest.mock import MagicMock, patch

import pytest

# Import this at the top level to avoid pylint import-outside-toplevel warnings
try:
    from api.config import settings as api_settings
except ImportError:
    # Mock if not available
    api_settings = MagicMock()

# Add the project root and src directory to Python path before any imports
project_root = Path(__file__).parent.parent.resolve()
src_path = project_root / "src"

# Add paths to sys.path if they're not already there
paths_to_add = [
    str(project_root),  # Project root
    str(src_path),  # src directory
]

# Add each path to the front of sys.path if not already present
for path in paths_to_add:
    if path not in sys.path:
        sys.path.insert(0, path)
        print(f"Added to sys.path: {path}")

# Import centralized fixtures
pytest_plugins = [
    "tests.fixtures.data",
    "tests.fixtures.async_utils",
    "tests.fixtures.model",
    "tests.fixtures.database",
    "tests.fixtures.mocks",  # Add the new mocks module
]


def pytest_configure(config):
    """Register custom markers for the mocking system."""
    config.addinivalue_line("markers", "mock_api: mock API modules")
    config.addinivalue_line("markers", "mock_database: mock database modules")
    config.addinivalue_line("markers", "mock_ml: mock machine learning modules")
    config.addinivalue_line("markers", "mock_all: mock all modules")
    config.addinivalue_line(
        "markers", "auto_mock: automatically mock modules for tests"
    )
    config.addinivalue_line("markers", "no_mocks: disable automatic mocking")


# The setup_mock_imports fixture is replaced by the more flexible mock_modules fixture
# in tests.fixtures.mocks


@pytest.fixture
def set_supabase_profile():
    """Fixture to temporarily set the Supabase profile.

    This fixture provides a context manager that temporarily changes the
    Supabase profile and restores it after the test completes.

    Returns:
        A function that takes a profile name and yields within a context
        where that profile is active.
    """

    def _set_profile(profile):
        # Store the original profile to restore it later
        original_profile = api_settings.SUPABASE_PROFILE
        # Update to the new profile
        api_settings.model_copy(update={"SUPABASE_PROFILE": profile})
        yield
        # Restore the original profile
        api_settings.model_copy(update={"SUPABASE_PROFILE": original_profile})

    return _set_profile


@pytest.fixture(autouse=True)
def mock_env_vars():
    """Mock environment variables for all tests.

    This fixture is automatically applied to all tests and provides consistent
    environment variables for Supabase credentials across all test modules.

    The values are standardized for testing:
    - Development: https://dev.supabase.co with key 'dev-key'
    - Staging: https://staging.supabase.co with key 'staging-key'
    - Production: https://prod.supabase.co with key 'prod-key'

    Yields:
        None: This fixture uses a context manager to temporarily modify
        environment variables during test execution.
    """
    env_vars = {
        "COINY_CLASSIFIER_SUPABASE_DEV_URL": "https://dev.supabase.co",
        "COINY_CLASSIFIER_SUPABASE_DEV_ANON_KEY": "dev-key",
        "COINY_CLASSIFIER_SUPABASE_STAGING_URL": "https://staging.supabase.co",
        "COINY_CLASSIFIER_SUPABASE_STAGING_ANON_KEY": "staging-key",
        "COINY_CLASSIFIER_SUPABASE_URL": "https://prod.supabase.co",
        "COINY_CLASSIFIER_SUPABASE_ANON_KEY": "prod-key",
    }

    with patch.dict("os.environ", env_vars, clear=True):
        # Only reload the config if it's not a mock
        if "api" in sys.modules and hasattr(sys.modules["api"], "config"):
            config = sys.modules["api"].config
            # Check if it's not a mock
            if not hasattr(config, "__class__") or "Mock" not in str(config.__class__):
                try:
                    # Use the already imported reload function
                    reload(config)
                except (ImportError, TypeError):
                    # If we can't reload, just continue
                    pass
        yield
