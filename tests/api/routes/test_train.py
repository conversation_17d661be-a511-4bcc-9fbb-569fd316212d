"""
Tests for the train endpoint.
"""

import pytest
from fastapi import status
from fastapi.testclient import TestClient

# Import the app
from api.main import app
from database.services import ModelMetadataError, TrainingDataError
from tests.api.routes.test_helpers import get_mock_training_data, verify_train_response
from tests.fixtures.data import get_sample_model_run_data, get_sample_model_version_data


@pytest.fixture(name="client")
def fixture_client():
    """Create a test client for the FastAPI app."""
    return TestClient(app)


def test_train_endpoint_success(client, monkeypatch):
    """
    Test the train endpoint with valid data.

    This test verifies that the train endpoint returns a 200 OK response
    when provided with valid data and the database queries succeed.
    """

    # Mock the dependencies
    def mock_get_profile():
        return "development"

    async def mock_get_training_data(
        *args, **kwargs
    ):  # pylint: disable=unused-argument
        return get_mock_training_data()

    # Apply the mocks
    monkeypatch.setattr("api.utils.get_supabase_profile", mock_get_profile)
    monkeypatch.setattr(
        "database.services.training_data_service.TrainingDataService.get_training_data",
        mock_get_training_data,
    )

    # Define test data
    payload = {"model_run_uuid": "f72599b5-942e-4877-bbea-1717244908da"}

    # Make request
    response = client.post(
        "/api/v1/train", json=payload, headers={"X-Supabase-Profile": "development"}
    )

    # Verify response
    assert response.status_code == status.HTTP_200_OK
    response_data = response.json()

    # Verify the response using the helper function
    verify_train_response(response_data)


def test_train_endpoint_model_not_found(client, monkeypatch):
    """
    Test the train endpoint when the model version is not found.

    This test verifies that the train endpoint returns a 404 Not Found response
    when the specified model version does not exist.
    """

    # Mock the dependencies
    def mock_get_profile():
        return "development"

    async def mock_get_model_metadata(
        *args, **kwargs
    ):  # pylint: disable=unused-argument
        raise ModelMetadataError("Model run not found with UUID: nonexistent-uuid")

    # Apply the mocks
    monkeypatch.setattr("api.utils.get_supabase_profile", mock_get_profile)
    monkeypatch.setattr(
        "database.services.model_metadata_service.ModelMetadataService.get_model_metadata",
        mock_get_model_metadata,
    )

    # Define test data
    payload = {"model_run_uuid": "nonexistent-uuid"}

    # Make request
    response = client.post(
        "/api/v1/train", json=payload, headers={"X-Supabase-Profile": "development"}
    )

    # Verify response
    assert response.status_code == status.HTTP_404_NOT_FOUND
    response_data = response.json()
    assert "not found" in response_data["detail"].lower()
    assert "nonexistent-uuid" in response_data["detail"]


def test_train_endpoint_dataset_not_found(client, monkeypatch):
    """
    Test the train endpoint when the dataset is not found.

    This test verifies that the train endpoint returns a 404 Not Found response
    when the specified dataset does not exist.
    """

    # Mock the dependencies
    def mock_get_profile():
        return "development"

    async def mock_get_model_metadata(
        *args, **kwargs
    ):  # pylint: disable=unused-argument
        # Get sample data from fixtures
        sample_model_version = get_sample_model_version_data()
        sample_model_run = get_sample_model_run_data()
        return {
            "model": {"uuid": "model-123", "name": "ResNet50"},
            "model_version": sample_model_version,
            "model_run": sample_model_run,
        }

    async def mock_get_training_data(
        *args, **kwargs
    ):  # pylint: disable=unused-argument
        raise TrainingDataError("Dataset not found with UUID: nonexistent-uuid")

    # Apply the mocks
    monkeypatch.setattr("api.utils.get_supabase_profile", mock_get_profile)
    monkeypatch.setattr(
        "database.services.model_metadata_service.ModelMetadataService.get_model_metadata",
        mock_get_model_metadata,
    )
    monkeypatch.setattr(
        "database.services.training_data_service.TrainingDataService.get_training_data",
        mock_get_training_data,
    )

    # Define test data
    payload = {"model_run_uuid": "f72599b5-942e-4877-bbea-1717244908da"}

    # Make request
    response = client.post(
        "/api/v1/train", json=payload, headers={"X-Supabase-Profile": "development"}
    )

    # Verify response
    assert response.status_code == status.HTTP_404_NOT_FOUND
    response_data = response.json()
    assert (
        "dataset not found" in response_data["detail"].lower()
        or "not found" in response_data["detail"].lower()
    )


def test_train_endpoint_invalid_request(client):
    """
    Test the train endpoint with invalid request data.

    This test verifies that the train endpoint returns a 422 Unprocessable Entity
    response when the request data is invalid.
    """
    # Define test data with missing required fields
    payload = {
        # Missing model_run_uuid
    }

    # Make request
    response = client.post(
        "/api/v1/train", json=payload, headers={"X-Supabase-Profile": "development"}
    )

    # Verify response
    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    response_data = response.json()
    assert (
        "model_run_uuid" in str(response_data).lower()
    )  # Field name should be in the error


def test_train_endpoint_database_error(client, monkeypatch):
    """
    Test the train endpoint when a database error occurs.

    This test verifies that the train endpoint returns a 422 Unprocessable Entity
    response when a database error occurs.
    """

    # Mock the dependencies
    def mock_get_profile():
        return "development"

    async def mock_get_model_metadata(
        *args, **kwargs
    ):  # pylint: disable=unused-argument
        raise ConnectionError("Database connection failed")

    # Apply the mocks
    monkeypatch.setattr("api.utils.get_supabase_profile", mock_get_profile)
    monkeypatch.setattr(
        "database.services.model_metadata_service.ModelMetadataService.get_model_metadata",
        mock_get_model_metadata,
    )

    # Define test data
    payload = {"model_run_uuid": "f72599b5-942e-4877-bbea-1717244908da"}

    # Make request
    response = client.post(
        "/api/v1/train", json=payload, headers={"X-Supabase-Profile": "development"}
    )

    # Verify response
    assert response.status_code == status.HTTP_503_SERVICE_UNAVAILABLE
    response_data = response.json()
    assert "detail" in response_data
    assert "database connection failed" in response_data["detail"].lower()
