"""
Tests for the Supabase configuration settings.
"""

import os
import sys
from unittest.mock import patch

import pytest
from api.config import Settings, SupabaseCredentials

# Add the project root to the Python path if it's not already there
project_root = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
)
if project_root not in sys.path:
    sys.path.insert(0, project_root)


# Create a test class to group related tests
class TestSupabaseConfig:
    """Test cases for Supabase configuration."""

    def test_supabase_credentials(self):
        """Test that SupabaseCredentials model works correctly."""
        # Create credentials
        credentials = SupabaseCredentials(
            url="https://test.supabase.co", anon_key="test-key"
        )

        # Verify attributes
        assert credentials.url == "https://test.supabase.co"
        assert credentials.anon_key == "test-key"

    def test_supabase_credentials_validation(self):
        """Test that SupabaseCredentials validates input correctly."""
        # Test with valid credentials
        credentials = SupabaseCredentials(
            url="https://example.com", anon_key="valid-key"
        )
        assert credentials.url == "https://example.com"
        assert credentials.anon_key == "valid-key"

    def test_settings_defaults(self):
        """Test that Settings has correct defaults."""
        settings = Settings()
        assert settings.API_HOST == "0.0.0.0"
        assert settings.API_PORT == 8000
        assert settings.API_VERSION == "v1"
        assert settings.API_PREFIX == "/api/v1"
        assert settings.SUPABASE_PROFILE == "development"

    def test_active_supabase_credentials_development(self):
        """Test that active_supabase_credentials returns development credentials."""
        settings = Settings(SUPABASE_PROFILE="development")
        credentials = settings.active_supabase_credentials
        assert credentials.url == settings.SUPABASE_DEV_URL
        assert credentials.anon_key == settings.SUPABASE_DEV_ANON_KEY

    def test_active_supabase_credentials_staging(self):
        """Test that active_supabase_credentials returns correct staging credentials."""
        settings = Settings(SUPABASE_PROFILE="staging")
        credentials = settings.active_supabase_credentials
        assert credentials.url == settings.SUPABASE_STAGING_URL
        assert credentials.anon_key == settings.SUPABASE_STAGING_ANON_KEY

    def test_active_supabase_credentials_production(self):
        """Test that active_supabase_credentials returns production credentials."""
        settings = Settings(SUPABASE_PROFILE="production")
        credentials = settings.active_supabase_credentials
        assert credentials.url == settings.SUPABASE_URL
        assert credentials.anon_key == settings.SUPABASE_ANON_KEY

    def test_active_supabase_credentials_empty_profile(self):
        """Test that active_supabase_credentials defaults to development."""
        settings = Settings(SUPABASE_PROFILE="")
        credentials = settings.active_supabase_credentials
        assert credentials.url == settings.SUPABASE_DEV_URL
        assert credentials.anon_key == settings.SUPABASE_DEV_ANON_KEY

    def test_settings_with_environment_variables(self):
        """Test that Settings loads environment variables correctly."""
        with patch.dict(
            os.environ,
            {
                "COINY_CLASSIFIER_SUPABASE_URL": "https://test.supabase.co",
                "COINY_CLASSIFIER_SUPABASE_ANON_KEY": "test-anon-key",
            },
            clear=True,
        ):
            settings = Settings()
            # Check that the production URL is set from the environment variable
            assert settings.SUPABASE_URL == "https://test.supabase.co"
            assert settings.SUPABASE_ANON_KEY == "test-anon-key"
            # We're not setting DEV URL in this test, so it should use the default
            # from the Settings class or be empty if no default is provided

    def test_settings_with_missing_env_vars(self):
        """Test that Settings handles missing environment variables gracefully."""
        with patch.dict(
            os.environ,
            {
                "COINY_CLASSIFIER_SUPABASE_DEV_URL": "",
                "COINY_CLASSIFIER_SUPABASE_DEV_ANON_KEY": "",
            },
            clear=True,
        ):
            settings = Settings()
            assert settings.SUPABASE_DEV_URL == ""
            assert settings.SUPABASE_DEV_ANON_KEY == ""

    @pytest.mark.parametrize(
        "profile,expected_url,expected_key",
        [
            ("development", "https://dev.supabase.co", "dev-key"),
            ("staging", "https://staging.supabase.co", "staging-key"),
        ],
    )
    def test_active_supabase_credentials_parametrized(
        self, profile, expected_url, expected_key
    ):
        """Test that active_supabase_credentials returns correct credentials."""
        settings = Settings(
            SUPABASE_PROFILE=profile,
            SUPABASE_DEV_URL="https://dev.supabase.co",
            SUPABASE_DEV_ANON_KEY="dev-key",
            SUPABASE_STAGING_URL="https://staging.supabase.co",
            SUPABASE_STAGING_ANON_KEY="staging-key",
            SUPABASE_URL="https://prod.supabase.co",
            SUPABASE_ANON_KEY="prod-key",
        )
        credentials = settings.active_supabase_credentials
        assert credentials.url == expected_url
        assert credentials.anon_key == expected_key
