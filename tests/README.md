# Testing Documentation for Coiny Classifier

This directory contains tests for the Coiny Classifier project. The tests are organized by component to make it easier to find and maintain tests as the project grows.

## Directory Structure

```bash
tests/
├── __init__.py
├── conftest.py                  # Global test fixtures and configuration
├── README.md                    # This file
├── api/                         # Tests for API components
│   ├── __init__.py
│   ├── conftest.py              # API-specific fixtures
│   ├── test_endpoints.py        # Tests for API endpoints
│   ├── test_middleware.py       # Tests for middleware components
│   └── test_config.py           # Tests for API configuration
├── database/                    # Tests for database components
│   ├── __init__.py
│   ├── conftest.py              # Database-specific fixtures
│   ├── test_supabase_client.py  # Tests for Supabase client
│   └── models/                  # Tests for database models
│       ├── __init__.py
│       └── test_dataset.py      # Tests for dataset model
├── fixtures/                    # Reusable test fixtures
├── helpers/                     # Test helper utilities
├── integration/                 # Integration tests across components
│   ├── __init__.py
│   ├── conftest.py              # Integration test fixtures
│   └── test_supabase_api.py     # Tests for Supabase and API integration
├── manual/                      # Manual tests or scripts
│   └── README.md                # Describes manual tests
├── models/                      # Tests for ML models
│   ├── __init__.py
│   ├── conftest.py              # ML-specific fixtures
│   └── test_dynamic_cnn_pipeline.py  # Tests for dynamic CNN model
└── unit/                        # Unit tests for individual modules
    └── README.md                # Describes unit tests
```

## Running Tests

### Running All Tests

```bash
pytest
```

### Running Tests for a Specific Component

```bash
# Run all API tests
pytest tests/api/

# Run all database tests
pytest tests/database/

# Run all model tests
pytest tests/models/

# Run all integration tests
pytest tests/integration/
```

### Running a Specific Test File

```bash
# Run a specific test file
pytest tests/api/test_endpoints.py
```

### Running with Coverage

```bash
# Run with coverage for all components
pytest --cov=src

# Run with coverage for specific components
pytest --cov=src/api tests/api/
pytest --cov=src/database tests/database/
pytest --cov=src/models tests/models/
```

## Test Organization

### API Tests

Tests in the `api/` directory focus on the REST API components:

- Endpoints
- Middleware
- Configuration

### Database Tests

Tests in the `database/` directory focus on database-related components:

- Supabase client
- Database models

### Model Tests

Tests in the `models/` directory focus on machine learning models:

- CoinsNet model
- Model training
- Model inference

### Integration Tests

Tests in the `integration/` directory focus on testing the integration between different components:

- API and database integration
- API and model integration

## Test Fixtures

Each component has its own `conftest.py` file with fixtures specific to that component. Global fixtures that are used across multiple components are defined in the root `conftest.py` file.

## Adding New Tests

When adding new tests:

1. Place the test in the appropriate directory based on the component it tests
2. Use the naming convention `test_*.py` for test files
3. Use the naming convention `test_*` for test functions
4. Use fixtures from the component's `conftest.py` file when possible
5. Add component-specific fixtures to the component's `conftest.py` file
6. Add global fixtures to the root `conftest.py` file

## Testing Environment Setup

The project is configured to ensure consistent test behavior across different environments:

1. **Local Development**: Running tests directly with `pytest`
2. **Pre-commit Hooks**: Tests run through pre-commit hooks
3. **CI/CD Pipeline**: Tests run in continuous integration

### Key Configuration Files

- **pyproject.toml**: Contains pytest configuration and Python path settings
- **.pre-commit-config.yaml**: Configures pre-commit hooks with environment variables
- **tests/conftest.py**: Contains shared test fixtures

## Best Practices for Reliable Tests

### Pytest Fixture Best Practices

#### 1. Avoid Redefined-Outer-Name Warnings

One common issue in pytest is the `redefined-outer-name` warning, which occurs when a test function parameter has the same name as a fixture. This can lead to confusion and potential bugs.

```python
# BAD: This will trigger a redefined-outer-name warning
@pytest.fixture
def model():
    return SimpleModel()

def test_model_prediction(model):
    # The parameter 'model' shadows the fixture 'model'
    model = model.train()  # This is confusing! Which model are we referring to?
    assert model.predict() == expected_result
```

Solutions:

1. **Use different names for local variables inside test methods**:

```python
# GOOD: Use a prefix for local variables
def test_model_prediction(model):
    test_model = model.train()  # Clear distinction between fixture and local variable
    assert test_model.predict() == expected_result
```

1. **Use the `request` fixture to access other fixtures**:

```python
# GOOD: Use request.getfixturevalue() to avoid parameter naming conflicts
def test_model_prediction(request):
    model = request.getfixturevalue("model")
    trained_model = model.train()
    assert trained_model.predict() == expected_result
```

1. **Avoid variable name conflicts in fixtures themselves**:

```python
# BAD: The fixture parameter has the same name as the local variable
@pytest.fixture
def temp_dir():
    with tempfile.TemporaryDirectory() as temp_dir:
        yield temp_dir  # This triggers a warning

# GOOD: Use different names
@pytest.fixture
def temp_dir():
    with tempfile.TemporaryDirectory() as temp_dir_path:
        yield temp_dir_path
```

#### 2. Fixture Dependency Best Practices

- **Be explicit about fixture dependencies**:

```python
# GOOD: Explicitly declare fixture dependencies
@pytest.fixture
def model_trainer(model, optimizer, dataset):
    return ModelTrainer(model, optimizer, dataset)
```

- **Use `usefixtures` for fixtures that are needed but not directly used**:

```python
# GOOD: Use usefixtures for setup/teardown fixtures
@pytest.mark.usefixtures("database_connection")
def test_database_query():
    # The database_connection fixture is used for setup but not directly accessed
    result = execute_query("SELECT * FROM table")
    assert result is not None
```

#### 3. Fixture Scope Management

- **Choose appropriate fixture scopes**:

```python
# Expensive setup should use broader scopes
@pytest.fixture(scope="module")
def large_dataset():
    # This will only be created once per module
    return load_large_dataset()

# Stateful fixtures that need to be reset should use narrower scopes
@pytest.fixture(scope="function")
def database():
    # This will be reset for each test function
    db = create_database()
    yield db
    db.reset()
```

#### 4. Fixture Organization

- **Place shared fixtures in conftest.py**
- **Place component-specific fixtures in component-specific conftest.py files**
- **Document fixture purpose with clear docstrings**

```python
@pytest.fixture
def model():
    """Create a trained model instance for testing.
    
    Returns a model with pre-loaded weights for consistent test results.
    """
    model = SimpleModel()
    model.load_weights("test_weights.pt")
    return model
```

### 1. Always Use Absolute Imports

```python
# Good - absolute imports
from src.models.CoinsNet import create_coins_net

# Bad - relative imports
from models.CoinsNet import create_coins_net
```

### 2. Use Absolute Paths for File Operations

```python
# Good - absolute path construction
def get_trained_models_dir():
    current_file = Path(os.path.abspath(__file__))
    project_root = current_file.parent.parent.parent
    models_dir = project_root / "src" / "models" / "trained"
    return str(models_dir)

# Bad - relative paths
DIR_TRAINED_MODELS = "src/models/trained"
```

### 3. Ensure All Directories Have `__init__.py` Files

All directories in the project should have an `__init__.py` file to ensure they are recognized as Python packages, which is essential for reliable imports.

### 4. Handle Environment Detection in Tests

```python
def is_running_in_pre_commit():
    """Detect if we're running in pre-commit mode."""
    pytest_opts = os.environ.get('PYTEST_ADDOPTS', '')
    if '--import-mode=importlib' in pytest_opts:
        return True

    cwd = os.getcwd()
    if '.git/hooks' in cwd or 'pre-commit' in cwd:
        return True
    
    return False

# Skip tests that don't work in pre-commit mode
@pytest.mark.skipif(IN_PRE_COMMIT, reason="Test skipped in pre-commit mode")
def test_problematic_function():
    # Test implementation
```

### 5. Use Robust Module Loading

```python
def get_module():
    """Get a module using multiple fallback approaches."""
    # Try absolute import first
    try:
        import src.models.SomeModule as module
        return module
    except ImportError:
        # Try alternative approaches...
```

## Common Issues and Solutions

### 1. Mocking Issues

**Problem**: Mocks fail when using relative imports or system-dependent paths.

**Solution**:

- Always use absolute imports
- Use absolute paths for file operations
- Use context managers for patching
- Use `autospec=True` for more accurate mocking

```python
# Good mocking approach
with patch('src.models.CoinsNet.load_model', autospec=True) as mock_load:
    # Test code
```

#### Using `autospec=True`

The `autospec=True` parameter creates a mock that automatically inherits the specifications of the object being patched:

- The mock will have the same attributes and methods as the original object
- If you try to access an attribute or call a method that doesn't exist on the original, you'll get an `AttributeError`
- Method signatures are enforced, so calling a method with incorrect arguments will raise a `TypeError`

This helps catch interface changes early and makes tests more robust. It can be used with both context managers and decorators:

```python
# With context manager
with patch('src.models.CoinsNet.load_model', autospec=True) as mock_load:
    # Test code

# With decorator
@patch('torch.load', autospec=True)
def test_function(mock_torch_load):
    # Test code
```

### 2. Path Resolution Issues

**Problem**: Tests fail because they can't find files in different environments.

**Solution**:

- Use Path objects for path manipulation
- Construct absolute paths from the current file location
- Ensure directories exist before using them

### 3. Import Mode Conflicts

**Problem**: Tests pass locally but fail in pre-commit hooks due to different import modes.

**Solution**:

- Use the `--import-mode=importlib` option consistently
- Set `PYTHONPATH=.:src` in all environments
- Skip problematic tests in specific environments

## Environment Variables

The following environment variables are set in pre-commit hooks:

```yaml
env:
  - PYTHONPATH=.:src
  - PYTEST_ADDOPTS=--import-mode=importlib
```

These ensure consistent behavior across environments.

## Test Skipping Strategy

Some tests are skipped in pre-commit mode due to mocking limitations. This is a deliberate choice to ensure tests don't fail in pre-commit hooks while still providing full test coverage in local development.

## Common Pytest Linting Issues and Solutions

### 1. Redefined-Outer-Name (W0621)

This is one of the most common pylint warnings in pytest code. It occurs when a test function parameter has the same name as a fixture.

```python
# This triggers W0621: redefined-outer-name
def test_function(model):  # 'model' is also the name of a fixture
    model = modify_model(model)  # Confusing reuse of the name
```

**Solutions:**

1. Use the `request` fixture to access other fixtures indirectly:

```python
def test_function(request):
    model = request.getfixturevalue("model")
    modified_model = modify_model(model)
```

1. Use prefixed variable names to distinguish local variables from fixtures:

```python
def test_function(model):
    test_model = modify_model(model)
```

1. In fixtures, avoid using the same name for local variables and the fixture itself:

```python
@pytest.fixture
def temp_dir():
    with tempfile.TemporaryDirectory() as temp_dir_path:  # Not 'temp_dir'
        yield temp_dir_path
```

### 2. Unused Argument (W0613)

This occurs when a test function accepts a fixture but doesn't use it directly.

```python
# This triggers W0613: unused-argument
def test_function(database):  # 'database' is never used in the function
    result = get_result()
    assert result is not None
```

**Solutions:**

1. Use `@pytest.mark.usefixtures` instead of accepting the fixture as a parameter:

```python
@pytest.mark.usefixtures("database")
def test_function():
    result = get_result()
    assert result is not None
```

1. If you need the fixture for setup but not in the test body, consider restructuring your fixtures.

### 3. Too Many Arguments (R0913)

This occurs when a test function has too many parameters (often fixtures).

**Solutions:**

1. Create composite fixtures that combine multiple dependencies:

```python
@pytest.fixture
def test_environment(model, optimizer, dataset, config):
    return TestEnvironment(model, optimizer, dataset, config)

def test_function(test_environment):
    # Use the combined environment instead of individual fixtures
```

1. Use the `request` fixture to access multiple fixtures as needed:

```python
def test_function(request):
    model = request.getfixturevalue("model")
    # Only get other fixtures if needed
    if condition:
        optimizer = request.getfixturevalue("optimizer")
```

### 4. Function Too Complex (R0912, R0915)

Test functions should be simple and focused. If they're too complex, they're hard to maintain.

**Solutions:**

1. Break down complex tests into multiple smaller tests
2. Move complex setup logic into fixtures
3. Create helper functions for repetitive assertions

## Updating Pydantic Models

The current warnings about deprecated Pydantic features can be addressed by:

1. Replacing class-based `config` with `ConfigDict`
2. Updating `json_encoders` to use custom serializers

See the [Pydantic V2 Migration Guide](https://docs.pydantic.dev/2.11/migration/) for details.
