"""
Integration tests for ModelTrainer with data classes from src.train.data_classes.

These tests focus on the integration between ModelTrainer and:
- TrainingComponents
- TrainingConfig
- TrainingMetrics

The tests verify that data classes work correctly with the trainer in realistic scenarios.
"""

import tempfile
from unittest.mock import patch

import pytest
import torch
from torch import nn, optim
from torch.utils.data import DataLoader, TensorDataset

from src.train.data_classes import TrainingComponents, TrainingConfig, TrainingMetrics
from src.train.trainer import ModelTrainer, TrainerConfig


class SimpleModel(nn.Module):
    """A simple model for testing data class integration."""

    def __init__(self):
        super().__init__()
        self.linear = nn.Linear(5, 1)

    def forward(self, x):
        """Forward pass of the model."""
        return self.linear(x)


@pytest.fixture(name="data_class_test_components")
def fixture_data_class_test_components():
    """Create components for data class integration testing."""
    torch.manual_seed(42)
    model = SimpleModel()
    loss_fn = nn.MSELoss()
    optimizer = optim.SGD(model.parameters(), lr=0.01)

    return {"model": model, "loss_fn": loss_fn, "optimizer": optimizer}


@pytest.fixture(name="data_class_test_loaders")
def fixture_data_class_test_loaders():
    """Create data loaders for data class integration testing."""
    torch.manual_seed(42)
    x_train = torch.randn(16, 5)
    y_train = torch.randn(16, 1)
    x_test = torch.randn(8, 5)
    y_test = torch.randn(8, 1)

    train_dataset = TensorDataset(x_train, y_train)  # pylint: disable=duplicate-code
    test_dataset = TensorDataset(x_test, y_test)

    train_loader = DataLoader(train_dataset, batch_size=4)
    test_loader = DataLoader(test_dataset, batch_size=4)

    return {"train": train_loader, "test": test_loader}


class TestTrainingComponentsIntegration:
    """Integration tests for TrainingComponents with ModelTrainer."""

    def test_training_components_creation_and_usage(
        self, data_class_test_components, data_class_test_loaders
    ):
        """Test that TrainingComponents integrates correctly with trainer."""
        with tempfile.TemporaryDirectory() as temp_dir:
            training_config = {
                "model_id": "test_components",
                "run_output_dir": temp_dir,
                "epochs": 2,
            }

            config = TrainerConfig(
                model_components=data_class_test_components,
                data_loaders=data_class_test_loaders,
                training_config=training_config,
            )

            trainer = ModelTrainer(config)

            # Verify TrainingComponents was created correctly
            assert isinstance(trainer.components, TrainingComponents)
            assert trainer.components.model == data_class_test_components["model"]
            assert trainer.components.loss_fn == data_class_test_components["loss_fn"]
            assert (
                trainer.components.optimizer == data_class_test_components["optimizer"]
            )

            # Verify components work during training
            with patch.object(trainer, "_collect_resource_metrics"):
                metrics = trainer.train()

            assert metrics is not None
            assert len(metrics.train_losses) == 2

    def test_training_components_device_placement(
        self, data_class_test_components, data_class_test_loaders
    ):
        """Test that TrainingComponents handles device placement correctly."""
        with tempfile.TemporaryDirectory() as temp_dir:
            training_config = {
                "model_id": "test_device",
                "run_output_dir": temp_dir,
                "epochs": 1,
            }

            config = TrainerConfig(
                model_components=data_class_test_components,
                data_loaders=data_class_test_loaders,
                training_config=training_config,
            )

            trainer = ModelTrainer(config)

            # Verify model was moved to the correct device (handle device index differences)
            model_device = next(trainer.components.model.parameters()).device
            trainer_device = trainer.device
            assert model_device.type == trainer_device.type

            # Verify optimizer still works after device placement
            optimizer_param_device = trainer.components.optimizer.param_groups[0][
                "params"
            ][0].device
            assert optimizer_param_device.type == trainer_device.type


class TestTrainingConfigIntegration:
    """Integration tests for TrainingConfig with ModelTrainer."""

    def test_training_config_attribute_access(
        self, data_class_test_components, data_class_test_loaders
    ):
        """Test that TrainingConfig provides correct attribute access."""
        with tempfile.TemporaryDirectory() as temp_dir:
            training_config_dict = {
                "model_id": "test_config",
                "run_output_dir": temp_dir,
                "epochs": 3,
                "gradient_clip_max_norm": 2.0,
                "custom_param": "test_value",
            }

            config = TrainerConfig(
                model_components=data_class_test_components,
                data_loaders=data_class_test_loaders,
                training_config=training_config_dict,
            )

            trainer = ModelTrainer(config)

            # Verify TrainingConfig was created correctly
            assert isinstance(trainer.config, TrainingConfig)
            assert trainer.config.config == training_config_dict

            # Test attribute access through __getattr__
            assert trainer.config.model_id == "test_config"
            assert trainer.config.epochs == 3
            assert trainer.config.gradient_clip_max_norm == 2.0
            assert trainer.config.custom_param == "test_value"

            # Test accessing non-existent attribute
            with pytest.raises(AttributeError):
                _ = trainer.config.non_existent_attribute

    def test_training_config_learning_rate_schedule(
        self, data_class_test_components, data_class_test_loaders
    ):
        """Test TrainingConfig with learning rate schedule."""
        with tempfile.TemporaryDirectory() as temp_dir:
            training_config_dict = {
                "model_id": "test_lr_schedule",
                "run_output_dir": temp_dir,
                "epochs": 4,
                "learning_rate_schedule": [
                    {"epoch": 1, "rate": 0.01},
                    {"epoch": 3, "rate": 0.001},
                ],
            }

            config = TrainerConfig(
                model_components=data_class_test_components,
                data_loaders=data_class_test_loaders,
                training_config=training_config_dict,
            )

            trainer = ModelTrainer(config)

            # Verify learning rate schedule was processed correctly
            assert (
                trainer.config.learning_rate_schedule
                == training_config_dict["learning_rate_schedule"]
            )
            assert len(trainer.lr_epoch_map) == 2
            assert trainer.lr_epoch_map[1] == 0.01
            assert trainer.lr_epoch_map[3] == 0.001


class TestTrainingMetricsIntegration:
    """Integration tests for TrainingMetrics with ModelTrainer."""

    def test_training_metrics_collection_and_access(
        self, data_class_test_components, data_class_test_loaders
    ):
        """Test that TrainingMetrics collects and provides access to metrics correctly."""
        with tempfile.TemporaryDirectory() as temp_dir:
            training_config = {
                "model_id": "test_metrics",
                "run_output_dir": temp_dir,
                "epochs": 2,
            }

            config = TrainerConfig(
                model_components=data_class_test_components,
                data_loaders=data_class_test_loaders,
                training_config=training_config,
            )

            trainer = ModelTrainer(config)

            # Verify TrainingMetrics was created correctly
            assert isinstance(trainer.metrics, TrainingMetrics)

            # Add custom metrics to test integration
            trainer.add_custom_metric("test_metric", 1.5)
            trainer.add_custom_metric("test_metric", 2.5)

            # Mock resource collection to avoid system calls
            with patch.object(trainer, "_collect_resource_metrics") as mock_resource:

                def mock_collect():
                    trainer.metrics.resources["cpu_percent"].append(45.0)
                    trainer.metrics.resources["memory_percent"].append(55.0)

                mock_resource.side_effect = mock_collect

                metrics = trainer.train()

            # Verify metrics were collected correctly
            assert isinstance(metrics, TrainingMetrics)
            assert len(metrics.train_losses) == 2
            assert len(metrics.test_losses) == 2
            assert len(metrics.batch_metrics) > 0

            # Test custom metrics
            assert "test_metric" in metrics.custom_metrics
            assert metrics.custom_metrics["test_metric"] == [1.5, 2.5]

            # Test resource metrics
            assert len(metrics.resources["cpu_percent"]) > 0
            assert len(metrics.resources["memory_percent"]) > 0

            # Test timing metrics
            assert "start_time" in metrics.timing
            assert "end_time" in metrics.timing
            assert "total_training_time" in metrics.timing

    def test_training_metrics_get_method(
        self, data_class_test_components, data_class_test_loaders
    ):
        """Test TrainingMetrics.get() method integration."""
        with tempfile.TemporaryDirectory() as temp_dir:
            training_config = {
                "model_id": "test_get_method",
                "run_output_dir": temp_dir,
                "epochs": 1,
            }

            config = TrainerConfig(
                model_components=data_class_test_components,
                data_loaders=data_class_test_loaders,
                training_config=training_config,
            )

            trainer = ModelTrainer(config)

            # Add test data
            trainer.add_custom_metric("custom_test", 42)
            trainer.metrics.timing["test_timing"] = 123.45

            # Test get method
            assert trainer.metrics.get("custom_test") == [42]
            assert trainer.metrics.get("test_timing") == 123.45
            assert trainer.metrics.get("non_existent", "default") == "default"
            assert trainer.metrics.get("non_existent") is None

    def test_training_metrics_summary_integration(
        self, data_class_test_components, data_class_test_loaders
    ):
        """Test TrainingMetrics summary generation integration."""
        with tempfile.TemporaryDirectory() as temp_dir:
            training_config = {
                "model_id": "test_summary",
                "run_output_dir": temp_dir,
                "epochs": 2,
            }

            config = TrainerConfig(
                model_components=data_class_test_components,
                data_loaders=data_class_test_loaders,
                training_config=training_config,
            )

            trainer = ModelTrainer(config)

            with patch.object(trainer, "_collect_resource_metrics"):
                metrics = trainer.train()

            # Test summary generation
            summary = metrics.get_metric_summary()

            assert isinstance(summary, dict)
            assert "final_train_loss" in summary
            assert "final_test_loss" in summary
            assert "timing" in summary
            assert "resources" in summary

            # Verify summary contains expected values
            assert summary["final_train_loss"] == metrics.train_losses[-1]
            assert summary["final_test_loss"] == metrics.test_losses[-1]
