# Manual Test Scripts

This directory contains scripts for manually testing various components of the `coiny-classifier` project. These scripts are intended for ad-hoc testing, debugging, or deeper exploration of specific functionalities outside the automated test suite.

## Running Manual Tests

To run any manual test script:

1.  **Activate Virtual Environment**: Ensure your project's Python virtual environment is activated.
    ```bash
    source .env/bin/activate  # Or equivalent for your shell (e.g., .env\Scripts\activate for Windows)
    ```

2.  **Navigate to Project Root**: Open your terminal and change the directory to the root of the `coiny-classifier` project.

3.  **Execute the Script**: Run the desired script using the Python interpreter from your virtual environment. The scripts are structured to mirror the `src` directory.

    For example, to run the manual test for `MLModelFactory` located in `tests/manual/models/test_ml_factory_manual.py`:

    ```bash
    .env/bin/python tests/manual/models/test_ml_factory_manual.py
    ```

## Available Manual Tests

*   **`models/test_ml_factory_manual.py`**: 
    Tests the `MLModelFactory` for creating models, loss functions, optimizers, schedulers, and managing model persistence. It provides detailed print output for each step.

## Adding New Manual Tests

When adding a new manual test script:

1.  Place it in a subdirectory under `tests/manual/` that mirrors its corresponding module's location in the `src/` directory (e.g., a test for `src/data/dataset.py` would go into `tests/manual/data/test_dataset_manual.py`).
2.  Include clear instructions at the top of the script (in a docstring) on how to run it and what it tests.
3.  Ensure the script can be run from the project root directory and correctly imports necessary modules from `src/`.
4.  Update this `README.md` file to include the new script in the "Available Manual Tests" section.
