# Define consistent environment variables for all hooks
default_language_version:
  python: python3.12

# Set consistent environment variables for all hooks
default_stages: [pre-commit, pre-push]

repos:
  - repo: local
    hooks:
      - id: black
        name: black
        entry: .env/bin/black
        language: system
        types: [python]

      - id: flake8
        name: flake8
        entry: .env/bin/flake8
        language: system
        types: [python]

      - id: pylint
        name: pylint
        entry: .env/bin/pylint
        language: system
        types: [python]
        exclude: ^setup\.py$  # Exclude setup.py from pylint
        args:
          - --disable=R0903

      - id: pytest
        name: pytest
        entry: env PYTHONPATH=.:src PYTEST_ADDOPTS=--import-mode=importlib .env/bin/pytest
        language: system
        types: [python]
        pass_filenames: false
        # Set environment variables for pytest to ensure consistent behavior
        additional_dependencies: []
        always_run: true
        verbose: true
        stages: [pre-commit]
